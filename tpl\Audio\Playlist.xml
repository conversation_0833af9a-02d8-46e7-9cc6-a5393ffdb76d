{extends "../@layout.xml"}

{block title}
    {_playlist}
    {$playlist->getName()}
{/block}

{block headIncludes}
    <meta property="og:type" content="music.album">
    <meta property="og:title" content="{$playlist->getName()}">
    <meta property="og:url" content="{$playlist->getURL()}">
    <meta property="og:description" content="{$playlist->getDescription()}">
    <meta property="og:image" content="{$cover_url}">
    
    <script type="application/ld+json">
        {
            "@context": "http://schema.org/",
            "type": "MusicAlbum",
            "name": {$playlist->getName()},
            "url": {$playlist->getURL()}
        }
    </script>
{/block}

{block header}
    <a href="{$owner->getURL()}">{$owner->getCanonicalName()}</a>
    »
    <a href="/playlists{$ownerId}">{_playlists}</a>
    »
    {_playlist}
{/block}

{block content}
    {include "bigplayer.xml"}
    <script>
        window.__current_page_audio_context = {
            name: 'playlist_context',
            entity_id: {$playlist->getId()},
            page: {$page}
        }
    </script>
    
    <div class="playlistBlock">
        <div class="playlistCover">
            {if $cover}
            <a href="{$cover_url}" target="_blank">
                    <img onclick="OpenMiniature(event, {$cover_url}, null, {$cover->getPrettyId()}, null)" src="{$cover_url}" alt="{_playlist_cover}">
            </a>
            {else}
            <a>
                <img src="{$cover_url}" alt="{_playlist_cover}">
            </a>
            {/if}

            <div class="profile_links" n:if="isset($thisUser)">
                <a class="profile_link" href="/player/upload?playlist={$playlist->getId()}" n:if="$canEdit">{_upload_audio}</a>
                <a class="profile_link" href="/playlist{$playlist->getPrettyId()}/edit" n:if="$canEdit">{_edit_playlist}</a>
                <a class="profile_link" id="bookmarkPlaylist" data-id="{$playlist->getId()}" n:if="!$isBookmarked">{_bookmark}</a>
                <a class="profile_link" id="unbookmarkPlaylist" data-id="{$playlist->getId()}" n:if="$isBookmarked">{_unbookmark}</a>
            </div>
        </div>

        <div class='playlistWrapper'>
            <div class="playlistInfo">
                <h4>{$playlist->getName()}</h4>

                <div class="moreInfo">
                    {$playlist->getMetaDescription()|noescape}

                    <div style="margin-top: 11px;">
                        <span>{nl2br($playlist->getDescriptionHTML())|noescape}</span>
                    </div>
                    <hr>
                </div>
            </div>
            <div class="audiosContainer scroll_container infContainer" style="margin-top: 14px;">
                {if $count < 1}
                    {_empty_playlist}
                {else}  
                    <div class="scroll_node" n:foreach="$audios as $audio">
                        {include "player.xml", audio => $audio}
                    </div>

                    {include "../components/paginator.xml", conf => (object) [
                        "page"     => $page,
                        "count"    => $count,
                        "amount"   => sizeof($audios),
                        "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
                        "atBottom" => true,
                    ]}
                {/if}
            </div>
        </div>
    </div>
{/block}
