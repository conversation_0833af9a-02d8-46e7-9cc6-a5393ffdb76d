<div n:class="bigPlayer, ctx_place, $tidy ? tidy">
    <div class="bigPlayerWrapper">
        <div class="playButtons">
            <div class="playButton musicIcon" data-tip='simple-black' data-align="bottom-start" data-title="{_play_tip} [Space]"></div>
                
            <div class="arrowsButtons">
                <div class="nextButton musicIcon" data-tip='simple-black' data-align="bottom-start" data-title=""></div>
                <div class="backButton musicIcon" data-tip='simple-black' data-align="bottom-start" data-title=""></div>
            </div>
        </div>

        <div class="trackPanel">
            <div class="trackInfo">
                <div class="trackName">
                    <span class="trackPerformers">
                        <a>{_track_unknown}</a>
                    </span> —
                    <span>{_track_noname}</span>
                </div>

                <div class="timer">
                    <span class="time">00:00</span>
                    <span>/</span>
                    <span class="elapsedTime">-00:00</span>
                </div>
            </div>

            <div class="track">
                <div class="selectableTrack">
                    <div id='bigPlayerLengthSliderWrapper'>&nbsp;
                        <div class="slider"></div>
                    </div>
                    <div class='selectableTrackLoadProgress'>
                        <div class="load_bar"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="volumePanel">
            <div class="volumePanelTrack">
                <div class="selectableTrack">
                    <div id='bigPlayerVolumeSliderWrapper'>&nbsp;
                        <div class="slider"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="additionalButtons">
            <div class="repeatButton musicIcon" data-tip='simple-black' data-align="bottom-end" data-title="{_repeat_tip} [R]" ></div>
            <div class="shuffleButton musicIcon" data-tip='simple-black' data-align="bottom-end" data-title="{_shuffle_tip}"></div>
            <div class="deviceButton musicIcon" data-tip='simple-black' data-align="bottom-end" data-title="{_mute_tip} [M]"></div>
			<form name="status_popup_form" style="display: none !important;">
				<input type="text" name="status" size="50" value="{$thisUser->getStatus()}">
				<input type="checkbox" name="broadcast">
				<input type="hidden" name="hash" value="{$csrfToken}">
			</form>
			<div class="statusButton musicIcon{if $thisUser->isBroadcastEnabled()} pressed{/if}" data-tip="simple-black" data-align="bottom-end" data-title="{_broadcast_audio}"></div>
        </div>
    </div>
</div>
