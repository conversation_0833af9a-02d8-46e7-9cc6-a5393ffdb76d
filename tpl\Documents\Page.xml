{extends "../@layout.xml"}

{block title}
    {_document} "{ovk_proc_strtr($doc->getName(), 20)}"
{/block}

{block header}
    {$doc->getName()}
{/block}

{block content}
    <style>
        .sidebar, .page_header, .page_footer {
            opacity: 0;
            pointer-events: none;
        }

        .page_body {
            margin-top: -45px;
        }
    </style>

    <div class='media-page-wrapper photo-page-wrapper'>
        <div class='photo-page-wrapper-photo'>
            {if $doc->isGif()}
                <img class="main_doc_block" alt="doc image" src="{$doc->getURL()}" />
            {elseif $doc->isImage()}
                <img class="main_doc_block" alt="doc image" src="{$doc->getPreview()->getURLBySizeId('original')}" />
            {elseif $doc->isAudio()}
                <audio src="{$doc->getURL()}" controls></audio>
            {*{elseif $doc->isBook()}
                <iframe src="{$doc->getURL()}" frameborder="0"></iframe>*}
            {else}
                <a href="{$doc->getURL()}" download="{downloadable_name($doc->getName())}">
                    <input class="button" type="button" value="{_download_file}">
                </a>
            {/if}
        </div>

        <div class='ovk-photo-details'>
            <div class='media-page-wrapper-description'>
                <p n:if='sizeof($tags) > 0'>
                    {foreach $tags as $tag}
                        <a href="/search?section=docs&tags={urlencode($tag)}">
                            {$tag}{if $tag != $tags[sizeof($tags) - 1]},{/if}
                        </a>
                    {/foreach}
                </p>
                <div class='upload_time'>
                    {_info_upload_date}: {$doc->getPublicationTime()}
                </div>
            </div>

            <hr/>

            <div class="media-page-wrapper-details">
                <div class='media-page-wrapper-comments'></div>
                <div class='media-page-wrapper-actions docMainItem' data-context="page" data-id="{$doc->getPrettiestId()}">
                    {if !$doc->isOwnerHidden()}
                        {var $owner = $doc->getOwner()}
                        <a href="{$owner->getURL()}" class='media-page-author-block'>
                            <img class='cCompactAvatars' src="{$owner->getAvatarURL('miniscule')}">

                            <div class='media-page-author-block-name'>
                                <b>{$owner->getCanonicalName()}</b>
                            </div>
                        </a>
                    {/if}

                    {if isset($thisUser)}
                        <a n:if="$modifiable" class="profile_link" style="display:block;width:96%;" id="edit_icon">{_edit}</a>
                        <a n:if="!$modifiable" class="profile_link" style="display:block;width:96%;" id="report_icon">{_report}</a>
                        <a n:if="!$copied || $copied && $copyImportance" class="profile_link" style="display:block;width:96%;" id="add_icon">{_add}</a>
                        <a n:if="$copied && !$copyImportance" class="profile_link" style="display:block;width:96%;" id="remove_icon">{_remove}</a>
                    {/if}
                </div>
            </div>
        </div>
    </div>
{/block}
