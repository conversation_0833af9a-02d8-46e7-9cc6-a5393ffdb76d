{var $author = $note->getOwner()}
{var $comments = $note->getLastComments(3)}
{var $commentsCount = $note->getCommentsCount()}

{var $commentTextAreaId = $note === NULL ? rand(1,300) : $note->getId()}
<div class="scroll_node page_block">
    <div data-id="{$note->getPrettyId()}" class="post note_post">
        <div class="post_header">
            <a class="post_image{if $author->isOnline()} online{/if}" href="{$author->getURL()}">
                <img src="{$author->getAvatarURL('miniscule')}" width="50" class="post-avatar" />
            </a>
            <div class="post_header_info">
                <div class="post_author">
                    <a class="author" href="{$author->getURL()}">
                        {$author->getCanonicalName()}
                    </a>
                    <a class="page_verified" n:if="$author->isVerified()" href="/verify"></a>
                </div>
                <div class="post_date">
                    <a href="/note{$note->getPrettyId()}" class="post_link">
                        {$note->getPublicationTime()}
                        <span n:if="$note->getEditTime() > $note->getPublicationTime()" class="edited editedMark" title="{$note->getEditTime()}">({_edited_short})</span>
                    </a>
                </div>
                <div class="post_actions" n:if="$thisUser">
                    <div class="post_actions_icon"></div>
                    <div class="tippy-menu">
                        <a n:if="$note->canBeModifiedBy($thisUser)" href="/note{$note->getOwner()->getId()}_{$note->getVirtualId()}/edit" rel='nofollow'>{_edit}</a>
                        <a n:if="$note->canBeModifiedBy($thisUser) && !($forceNoDeleteLink ?? false)" class="delete" href="/note{$note->getOwner()->getId()}_{$note->getId()}/delete">{_delete}</a>
                        <a n:if="$thisUser->getId() != $note->getOwner()->getId()" class="report" href="javascript:reportNote({$note->getId()})">{_report}</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="post-content" id="{$note->getPrettyId()}">
            <div class="text wall_text" id="text{$note->getPrettyId()}">
                <a class="wall_note_type" href="/note{$note->getPrettyId()}">{$note->getName()}</a>
                <div n:ifcontent class="really_text wall_post_text note_text_preview">
                    {$note->getText()|noescape}
                </div>
            </div>
        </div>
        <div class='post_edit'></div>
        <div class="post_full_like_wrap" n:if="$thisUser && !($forceNoLike ?? false)" n:ifcontent>
            <a n:if="!($forceNoCommentsLink ?? false) && $commentsCount == 0" class="reply_link_wrap" href="javascript:window.toggle_comment_textarea({$commentTextAreaId})">{_comment}</a>
            <a n:if="!($forceNoShareLink ?? false)" class="share_link_wrap" href="javascript:repost({$note->getId()}, 'note')">{_share}</a>
        </div>
        <div n:if="!($forceNoCommentsLink ?? false)" id="commentTextArea{$commentTextAreaId}" n:class="post-menu-s, ($commentsCount == 0 ? 'hidden')">
            <a n:if="$commentsCount > 3" href="/note{$note->getPrettyId()}" class="button button_wide button_gray expand_button">{_view_other_comments}</a>
            <div n:ifcontent class="comments">
                {foreach $comments as $comment}
                    {include "../comment.xml", comment => $comment, $compact => true}
                {/foreach}
            </div>
            <div n:ifset="$thisUser" class="commentsTextFieldWrap">
                {var $commentsURL = "/al_comments/create/notes/" . $note->getId()}
                {include "../textArea.xml", route => $commentsURL, postOpts => false, graffiti => (bool) ovkGetQuirk("comments.allow-graffiti"), post => $note}
            </div>
        </div>
    </div>
</div>
