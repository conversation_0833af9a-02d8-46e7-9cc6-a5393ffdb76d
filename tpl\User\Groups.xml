{extends "../@listView.xml"}
{var $iterator = $user->getClubs($page, $admin)}
{var $count    = $user->getClubCount($admin)}

{block noscroll}{/block}
{block title}
    {_groups}
{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block tabs}
    {if !is_null($thisUser) && $user->getId() === $thisUser->getId()}
        {var $count = $user->getClubCount()}
        {var $tabs = [
            [
                'url' => "/groups{$user->getId()}",
                'title' => 'groups',
                'active' => !$admin,
                'id' => 'groups_tab_all',
                'count' => $count
            ],
            [
                'url' => "/groups{$user->getId()}?act=managed",
                'title' => 'managed',
                'active' => $admin,
                'id' => 'groups_tab_managed',
                'count' => $user->getClubCount(true)
            ]
        ]}
        {var $createGroupButton = '<a class="button" href="/groups_create">' . tr('create_group') . '</a>'}
        {include "../components/page_tabs_header.xml", tabs => $tabs, id => 'groups_top_tabs', extra => $createGroupButton}

        {include "../components/search_bar.xml",
            id => 'groups_search_header',
            placeholder => tr('search_by_groups'),
            action => '/search',
            section => 'groups',
            class => 'groups_search_header',
            onsubmit => 'this.submit(); return false;'
        }
    {/if}
{/block}

{block link|strip|stripHtml}
    {$x->getURL()}
{/block}

{block preview}
	<img src="{if str_contains($x->getAvatarUrl('miniscule'), 'camera_200.png')}
		/themepack/vkify16/{$theme->getVersion()}/resource/community_200.png
	{else}
		{$x->getAvatarUrl('miniscule')}
		{/if}" 
		alt="{$x->getCanonicalName()}" width="80" loading=lazy
	/>
{/block}

{block name}{/block}

{block infotable}
    <div class="labeled name">
        <a href="{$x->getURL()}">{$x->getName()}</a>
        <img n:if="$x->isVerified()"
                class="name-checkmark"
                src="/assets/packages/static/openvk/img/checkmark.png"
        />
    </div>
    <div class="labeled">
         <a href="/club{$x->getId()}/followers">{tr("participants", $x->getFollowersCount())}</a></td>
    </div>
{/block}

{block description}
    {$x->getDescription()}
{/block}

{block actions}
    <div class="post_actions">
        <div class="post_actions_icon"></div>
        <div class="tippy-menu">
            <a href="{$x->getURL()}">{_check_community}</a>
            {if $x->canBeModifiedBy($thisUser ?? NULL)}
                {var $clubPinned = $thisUser->isClubPinned($x)}
                <a href="/groups_pin?club={$x->getId()}&hash={rawurlencode($csrfToken)}" n:if="$clubPinned || $thisUser->getPinnedClubCount() <= 10" id="_pinGroup" data-group-name="{$x->getName()}" data-group-url="{$x->getUrl()}">
                    {if $clubPinned}
                        {_remove_from_left_menu}
                    {else}
                        {_add_to_left_menu}
                    {/if}
                </a>
            {/if}
            {if $x->getSubscriptionStatus($thisUser) == false}
                <a href="javascript:void(0)" onclick="this.closest('.tippy-menu').querySelector('form[data-action=add]').submit()">{_join_community}</a>
                <form style="display: none" data-action="add" action="/setSub/club" method="post">
                    <input type="hidden" name="act" value="add" />
                    <input type="hidden" name="id" value="{$x->getId()}" />
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                </form>
            {else}
                <a href="javascript:void(0)" onclick="this.closest('.tippy-menu').querySelector('form[data-action=rem]').submit()">{_leave_community}</a>
                <form style="display: none" data-action="rem" action="/setSub/club" method="post">
                    <input type="hidden" name="act" value="rem" />
                    <input type="hidden" name="id" value="{$x->getId()}" />
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                </form>
            {/if}
        </div>
    </div>
{/block}

{block bottom}
    {if !is_null($thisUser) && $user->getId() === $thisUser->getId()}
        <div class="page_block module page_list_module groups_module">
            <a href="/search?section=groups" class="module_header">
                <h3 class="header_top clear_fix">
                    <span class="header_label fl_l">
                        <vkifyloc name="recommended_groups"/>
                    </span>
                </h3>
            </a>
            <div class="module_body clear_fix">
                {var $clubsRepo = new \openvk\Web\Models\Repositories\Clubs()}
                {var $totalClubs = $clubsRepo->getCount()}
                {var $randomClubs = []}
                {if $totalClubs > 0}
                    {var $randomIds = []}
                    {var $attempts = 0}
                    {var $maxAttempts = min(50, $totalClubs * 2)}
                    {while count($randomClubs) < 5 && $attempts < $maxAttempts}
                        {var $randomId = rand(1, $totalClubs)}
                        {if !in_array($randomId, $randomIds)}
                            {var $randomClub = $clubsRepo->get($randomId)}
                            {if $randomClub && !$randomClub->isHideFromGlobalFeedEnabled() && !$randomClub->isHidingFromGlobalFeedEnforced() && !$randomClub->isBanned()}
                                {var $randomIds[] = $randomId}
                                {var $randomClubs[] = $randomClub}
                            {/if}
                        {/if}
                        {var $attempts = $attempts + 1}
                    {/while}
                {/if}
                {if count($randomClubs) > 0}
                    <div class="line_cell clear_fix" n:foreach="$randomClubs as $club">
                        <div class="thumb">
                            <a href="club{$club->getId()}">
                                <img class="cell_img" src="{if str_contains($club->getAvatarUrl(), 'camera_200.png')}
                                    /themepack/vkify16/{$theme->getVersion()}/resource/community_200.png
                                {else}
                                    {$club->getAvatarUrl()}
                                {/if}" alt="{$club->getCanonicalName()}" />
                            </a>
                        </div>
                        <div class="desc_info">
                            <div class="group_name"><a href="club{$club->getId()}">{$club->getName()}</a></div>
                            <div class="group_desc">
                                <span>{$club->getDescription()}</span>
                            </div>
                        </div>
                    </div>
                    <div class="line_cell">
                        <a href="/search?section=groups" class="button button_gray button_wide">{_show_more}</a>
                    </div>
                {else}
                    {include "../components/content_error.xml" description => tr("groups_zero")}
                {/if}
            </div>
        </div>
    {/if}
{/block}
