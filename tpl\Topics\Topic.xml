{extends "../@layout.xml"}
{block title}{_view_topic} "{$topic->getTitle()}"{/block}

{block content}
{include "../components/page_crumb_header.xml", crumbs: [
    ['href' => $club->getURL(), 'title' => $club->getCanonicalName()],
    ['href' => "/board{$club->getId()}", 'title' => tr("discussions")],
    ['title' => $topic->getTitle(), 'count' => $count]
], extra => '<a class="button" href="/topic'.$club->getId().'_'.$topic->getVirtualId().'/edit">'.tr("edit_topic_action").'</a>'
}
<div class="page_block">
    <div class="page_block_sub_header bt_header">
        {$topic->getTitle()}
    </div>
    {include "../components/comments.xml", comments => $comments, count => $count, page => $page, model => "topics", club => $club, readOnly => $topic->isClosed(), showTitle => false, parent => $topic}
{/block}
