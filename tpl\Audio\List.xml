{extends "../@layout.xml"}

{block title}
    {if $mode == 'list'}
        {if $ownerId > 0}
            {_audios} {$owner->getMorphedName("genitive", false)}
        {else}
            {_audios_group}
        {/if}
    {elseif $mode == 'new'}
        {_audio_new}
    {elseif $mode == 'uploaded'}
        {_my_audios_small_uploaded}
    {elseif $mode == 'popular'}
        {_audio_popular}
    {elseif $mode == 'alone_audio'}
        {$alone_audio->getName()}
    {else}
        {if $ownerId > 0}
            {_playlists} {$owner->getMorphedName("genitive", false)}
        {else}
            {_playlists_group}
        {/if}
    {/if}
{/block}

{block content}
    {* ref: https://archive.li/P32em *}

    <script>
        window.__current_page_audio_context = null
        {if $mode == 'list'}
            window.__current_page_audio_context = {
                name: 'entity_audios',
                entity_id: {$ownerId},
                page: {$page}
            }
        {elseif $mode == 'uploaded'}
            window.__current_page_audio_context = {
                name: 'uploaded',
                entity_id: 0,
                page: {$page}
            }
        {elseif $mode == 'alone_audio'}
            window.__current_page_audio_context = {
                name: 'alone_audio',
                entity_id: {$alone_audio->getId()},
                page: 1
            }
        {/if}
    </script>

    <div class="wide_column_left">
    <div class="narrow_column_wrap">
        <div class="narrow_column">
            {var $tabMenuItems = []}
            
            {* My Music tab *}
            {if isset($thisUser)}
                {var $tabMenuItems[] = [
                    'url' => "/audios" . $thisUser->getId(),
                    'title' => 'my_music',
                    'active' => $mode === 'list' && $isMy,
                    'id' => $mode === 'list' && $isMy ? 'mymussel' : null,
                    'extraItem' => [
                        'url' => "/player/upload",
                        'class' => "addAudioSmall",
                        'content' => "<div class='addIcon'></div>",
                        'onclick' => "showAudioUploadPopup(); return false;"
                    ]
                ]}
            {/if}

            {* Uploaded tab *}
            {var $tabMenuItems[] = [
                'url' => "/audios/uploaded",
                'title' => 'my_audios_small_uploaded',
                'active' => $mode === 'uploaded',
                'id' => $mode === 'uploaded' ? 'used' : 'ki'
            ]}

            {* New tab *}
            {if isset($thisUser)}
                {var $tabMenuItems[] = [
                    'url' => "/search?section=audios",
                    'title' => 'audio_new',
                    'active' => $mode === 'new',
                    'id' => $mode === 'new' ? 'used' : 'ki'
                ]}
            {/if}

            {* Popular tab *}
            {if isset($thisUser)}
                {var $tabMenuItems[] = [
                    'url' => "/search?section=audios&order=listens",
                    'title' => 'audio_popular',
                    'active' => $mode === 'popular',
                    'id' => $mode === 'popular' ? 'used' : 'ki'
                ]}
            {/if}

            {if isset($thisUser)}
                {* Add separator before playlists section *}
                {var $tabMenuItems[] = ['isSeparator' => true]}

                {* My Playlists tab *}
                {var $tabMenuItems[] = [
                    'url' => "/playlists" . $thisUser->getId(),
                    'title' => 'my_playlists',
                    'active' => $mode === 'playlists' && $ownerId == $thisUser->getId(),
                    'id' => $mode === 'playlists' && $ownerId == $thisUser->getId() ? 'used' : 'ki'
                ]}

                {var $tabMenuItems[] = [
                    'url' => "/audios/newPlaylist",
                    'title' => 'new_playlist',
                    'active' => false
                ]}
            {/if}

            {* Other user/club tabs *}
            {if !$isMy && $mode !== 'popular' && $mode !== 'new' && $mode != 'alone_audio' && $mode != 'uploaded'}
                {* Add separator before other user/club section *}
                {var $tabMenuItems[] = ['isSeparator' => true]}
                
                {var $tabMenuItems[] = [
                    'url' => "/audios" . $ownerId,
                    'title' => $ownerId > 0 ? 'music_user' : 'music_club',
                    'active' => $mode === 'list',
                    'id' => $mode === 'list' ? 'used' : 'ki',
                    'extraItem' => [
                        'url' => "/player/upload?gid=" . abs($ownerId),
                        'class' => "addAudioSmall",
                        'content' => "<div class='addIcon'></div>",
                        'condition' => isset($thisUser) && isset($club) && $club->canUploadAudio($thisUser)
                    ]
                ]}
                

                {if isset($thisUser) && isset($ownerId) && !$isMy}
                    {var $tabMenuItems[] = [
                        'url' => "/playlists" . $ownerId,
                        'title' => $ownerId > 0 ? 'playlists_user' : 'playlists_club',
                        'active' => $mode === 'playlists' && $ownerId != $thisUser->getId(),
                        'id' => $mode === 'playlists' && $ownerId != $thisUser->getId() ? 'used' : 'ki'
                    ]}
                {/if}

                {if isset($thisUser) && $isMyClub}
                    {var $tabMenuItems[] = [
                        'url' => "/audios/newPlaylist" . ($isMyClub ? "?gid=" . abs($ownerId) : ""),
                        'title' => 'new_playlist',
                        'active' => false
                    ]}
                {/if}
            {/if}

            {include "../components/ui_rmenu.xml", 
                items => $tabMenuItems, 
                additionalCssClass => 'audio_tabs', 
                id => 'audio_tabs'
            }

            {if $friendsAudios}
                {var $friendsMenuItems = []}
                {foreach $friendsAudios as $friend}
                    {var $friendsMenuItems[] = [
                        'url' => "/audios" . $friend->getRealId(),
                        'title' => $friend->getCanonicalName(),
                        'translate' => false,
                        'avatar' => $friend->getAvatarURL(),
                        'additionalInfo' => $audioStatus ? $audioStatus->getName() : tr("audios_count", $friend->getAudiosCollectionSize()),
                        'isRich' => true
                    ]}
                {/foreach}
                {include "../components/ui_rmenu.xml", 
                    items => $friendsMenuItems,
                    additionalCssClass => 'friends_audio_list',
                    id => 'friends_audio_list'
                }
            {/if}
        </div>
    </div>

    <div class="wide_column_wrap">
        <div class="wide_column">
            {include "bigplayer.xml", buttonsShow_summary => $audiosCount > 10}

            <div n:if="isset($audios)" class='summaryBarHideable summaryBar summaryBarFlex padding' style="margin: 0px -10px;width: 99.5%;display: none;">
                <div class='summary'>
                    <b>{tr("is_x_audio", $audiosCount)}</b>
                </div>

                {include "../components/paginator.xml", conf => (object) [
                    "page"     => $page,
                    "count"    => $audiosCount,
                    "amount"   => sizeof($audios),
                    "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
                    "atTop"    => true,
                    "space"    => 6,
                    "tidy"     => true,
                ]}
            </div>

            <div class="page_block audiosDiv">
                <div class="audiosContainer audiosSideContainer" n:if="$mode != 'playlists'">
                    <div n:if="$audiosCount <= 0" style='height: 100%;'>
                        {include "../components/content_error.xml", description => $ownerId > 0 ? ($ownerId == $thisUser->getId() ? tr("no_audios_thisuser") : tr("no_audios_user")) : tr("no_audios_club")}
                    </div>
                    <div n:if="$audiosCount > 0" class="scroll_container">
                        <div class="scroll_node" n:foreach="$audios as $audio">
                            {include "player.xml", audio => $audio, club => $club}
                        </div>
                    </div>

                    <div n:if="$mode != 'new' && $mode != 'popular'">
                        {include "../components/paginator.xml", conf => (object) [
                            "page"     => $page,
                            "count"    => $audiosCount,
                            "amount"   => sizeof($audios),
                            "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
                            "atBottom" => true,
                        ]}
                    </div>
                </div>
                
                <div class="audiosPaddingContainer audiosSideContainer audiosPaddingContainer" n:if="$mode == 'playlists'">
                    <div n:if="$playlistsCount <= 0" style='height: 100%;'>
                        {include "../components/content_error.xml", description => $ownerId > 0 ? ($ownerId == $thisUser->getId() ? tr("no_playlists_thisuser") : tr("no_playlists_user")) : tr("no_playlists_club")}
                    </div>

                    <div class="scroll_container playlistContainer" n:if="$playlistsCount > 0">
                        <div class='scroll_node' n:foreach='$playlists as $playlist'>
                            {include 'playlistListView.xml', playlist => $playlist}
                        </div>
                    </div>

                    <div>
                        {include "../components/paginator.xml", conf => (object) [
                            "page"     => $page,
                            "count"    => $playlistsCount,
                            "amount"   => sizeof($playlists),
                            "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
                            "atBottom" => true,
                        ]}
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}
