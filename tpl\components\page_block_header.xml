{* 
  Reusable page block header component
  
  @param title        Translation key or string for the header title
  @param count        (optional) Number or string to display as a count badge
  @param extra        (optional) HTML content to display on the right side of the header
  @param extra_left   (optional) HTML content to display on the left side of the header
  @param translate    (optional) Boolean, if false do not translate title
*}
<h2 class="page_block_h2">
  <div class="page_block_header clear_fix">
    {ifset $extra_left}
    <div class="page_block_header_extra_left _header_extra_left">
      {$extra_left}
    </div>
    {/ifset}
    {ifset $extra}
    <div class="page_block_header_extra _header_extra">
      {$extra|noescape}
    </div>
    {/ifset}
    <div class="page_block_header_inner _header_inner">
      {ifset $title}{if isset($translate) && $translate === false}{$title}{else}{tr($title)}{/if}{/ifset}
      {ifset $count}
        <span class="page_block_header_count" id="fw_summary">{$count}</span>
      {/ifset}
    </div>
  </div>
</h2> 