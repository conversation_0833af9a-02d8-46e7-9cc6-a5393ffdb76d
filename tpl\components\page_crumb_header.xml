{* 
  Reusable breadcrumb header component
  
  @param crumbs       Array of breadcrumb items with 'title', optional 'href' and 'condition' keys
  @param extra        (optional) HTML content to display on the right side of the header
  @param extra_left   (optional) HTML content to display on the left side of the header
*}
<h2 class="page_block_h2">
  <div class="page_block_header clear_fix">
    {ifset $extra_left}
    <div class="page_block_header_extra_left">
      {$extra_left}
    </div>
    {/ifset}
    {ifset $extra}
    <div class="page_block_header_extra">
      {$extra|noescape}
    </div>
    {/ifset}
    <div class="page_block_header_inner">
    {foreach $crumbs as $crumb}
      {if !isset($crumb['condition']) || $crumb['condition']}
        {if isset($crumb['href'])}
          {if !$iterator->isFirst()}
            <div class="ui_crumb_sep"></div>
          {/if}
          <a class="ui_crumb" href="{$crumb['href']}">{$crumb['title']}<span class="ui_crumb_count" n:if="isset($crumb['count'])">{$crumb['count']}</span></a>
        {else}
          {if !$iterator->isFirst()}
            <div class="ui_crumb_sep"></div>
          {/if}
          <div class="ui_crumb">{$crumb['title']}<span class="ui_crumb_count" n:if="isset($crumb['count'])">{$crumb['count']}</span></div>
        {/if}
      {/if}
    {/foreach}
  </div>
</h2>