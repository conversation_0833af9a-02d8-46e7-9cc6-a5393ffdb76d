{extends "../@layout.xml"}
{var $iterator = iterator_to_array($albums)}
{var $page     = $paginatorConf->page}

{block title}{_albums} {$owner->getCanonicalName()}{/block}

{block content}
    {var $isClub = ($owner instanceof \openvk\Web\Models\Entities\Club)}
    {var $createUrl = $isClub ? '/albums/create?gpid=' . $owner->getId() : '/albums/create'}
    {include "../components/page_crumb_header.xml", crumbs: [
        ['href' => $owner->getURL(), 'title' => $owner->getCanonicalName()],
        ['title' => tr("albums"), 'count' => $count]
    ], extra => '<a n:if="isset($thisUser) && $thisUser->getId() == $owner->getId()" class="button" href="' . $createUrl . '">' . tr('create_album') . '</a>'}

    <div class="page_block page_padding">
        <div n:ifcontent class="photo_items_list">
            {foreach $iterator as $x}
                <div class="clear_fix clear page_album_row">
                    <a href="/album{$x->getPrettyId()}" class="page_album_link {if is_null($x->getCoverPhoto())}page_album_nocover{/if}">
                        {var $cover = $x->getCoverPhoto()}
                        <div class="page_album_thumb_wrap">
                            <img n:if="!is_null($cover)"
                                src="{$cover->getURLBySizeId('normal')}"
                                class="page_album_thumb" loading="lazy" />
                        </div>
                        <div class="page_album_title">
                            <div class="page_album_size">{$x->getPhotosCount()}</div>
                            <div class="page_album_title_text">{$x->getName()}</div>
                            <div class="page_album_description">{$x->getDescription()}</div>
                        </div>
                    </a>
                </div>
            {/foreach}
        </div>
        {if $count == 0}
            {include "../components/content_error.xml", description => tr("albums_zero")}
        {/if}
    </div>
{/block}
