{var $preview = $doc->hasPreview() ? $doc->getPreview() : NULL}
{var $copied = !isset($club) ? $doc->isCopiedBy($thisUser) : $doc->isCopiedBy($club)}
{var $modifiable = $doc->canBeModifiedBy($thisUser)}

<a href="/doc{$doc->getPrettyId()}?key={$doc->getAccessKey()}" n:class="docMainItem, viewerOpener, docGalleryItem, $scroll_context ? scroll_node, $embed ? embeddable" data-id="{$doc->getPrettiestId()}">
    <img class="docGalleryItem_main_preview" loading="lazy" src="{$preview->getURLBySizeId('medium')}" alt="gallery photo">
    {if $embed}
        <div class="play-button">
            <div class="play-button-ico"></div>
        </div>
        <img class="docGalleryItem_gif_preview" loading="lazy" src="{$doc->getURL()}" alt="gif photo view">
    {/if}

    <div class="doc_top_panel doc_shown_by_hover" n:if="$thisUser">
        <div class="doc_volume_action" n:if="!$modifiable" id="report_icon"></div>
        <div class="doc_volume_action" n:if="$modifiable" id="edit_icon"></div>
        <div class="doc_volume_action" n:if="!$copied || $copied && $copyImportance" id="add_icon"></div>
        <div class="doc_volume_action" n:if="$copied && !$copyImportance" id="remove_icon"></div>
    </div>

    <div n:class="doc_bottom_panel, doc_shown_by_hover, doc_content, $showInfo ? info_shown">
        <span class="doc_bottom_panel_name noOverflow doc_name">{$doc->getName()}</span>
        <span class="doc_bottom_panel_size">{readable_filesize($doc->getFilesize())}</span>
    </div>
</a>
