{extends "../@layout.xml"}
{block title}{_edit_topic} "{$topic->getTitle()}"{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs: [
        ['href' => $club->getURL(), 'title' => $club->getCanonicalName()],
        ['href' => "/board{$club->getId()}", 'title' => tr("discussions")],
        ['title' => $topic->getTitle()]
    ]}
    <div class="page_block">
        <form method="POST" enctype="multipart/form-data">
            <div class="settings_panel group_settings settings_padding" style="width: 455px;margin-inline: auto;">
                <div class="settings_list_row">
                    <div class="settings_label">{_title}</div>
                    <div class="settings_labeled_text">
                        <input type="text" name="title" style="width: 100%;" value="{$topic->getTitle()}" />
                    </div>
                </div>
                <div class="settings_list_row">
                    <div class="settings_label">{_topic_settings}</div>
                    <div class="settings_labeled_text">
                        {if $topic->getClub()->canBeModifiedBy($thisUser)}
                            <label><input type="checkbox" name="pin" n:attr="checked => $topic->isPinned()" /> {_pin_topic}</label><br />
                        {/if}
                        <label><input type="checkbox" name="close" n:attr="checked => $topic->isClosed()" /> {_close_topic}</label>
                    </div>
                </div>
                <div class="settings_save_footer">
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                    <input type="submit" value="{_save}" class="button" />
                </div>
            </div>
            <input type="hidden" name="hash" value="{$csrfToken}" />
        </form>
    </div>
{/block}
