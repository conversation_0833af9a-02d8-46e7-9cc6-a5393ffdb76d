.settings_panel {
    margin: 0 20px;
}

.settings_list_row:not(:last-child) {
    margin-bottom: 10px;
}

.settings_label {
    color: var(--muted-text-color-2);
    width: 145px;
    padding: 6px 10px 6px 0;
    float: left;
    line-height: 16px;
}

.settings_labeled_text {
    padding-top: 6px;
    margin: 0 0 8px 155px;
}
.settings_list_row:last-child .settings_labeled_text {
    margin-bottom: 0;
}

.settings_list_row .settings_label,
.settings_list_row .settings_labeled_text {
    padding-top: 2px;
    line-height: 16px;
}

.settings_line {
	margin: 12px 0 15px;
	background: var(--border-color);
	height: 1px;
}

.settings_line.settings_line_no_padding {
    margin: 0;
}

.settings_block_msg {
    padding: 13px 20px 15px;
    border-bottom: 1px solid var(--border-color);
    line-height: 150%;
}

.settings_block_footer {
    border-top: 1px solid var(--border-color);
    background: var(--module-header-background-color);
    line-height: 140%;
    padding: 15px 0 17px;
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
    text-align: center;
}

.settings_list_row input:not([type="submit"], [type="checkbox"], [type="radio"]),.settings_list_row:not(.settings_privacy_row) select,.settings_list_row textarea {
    width: 300px;
}

.settings_list_row select + label {
    margin-top: 10px;
}
.settings_list_row label {
    margin-bottom: 10px;
    display: flex;
    width: fit-content;
}

.settings_list_row label.checkbox {
    display: block;
}

/* privacy */
.settings_privacy {
    margin: 0;
    padding: 6px 20px 5px
}

.settings_privacy_row {
    border-bottom: solid var(--border-color);
    border-width: 1px 0;
    padding: 11px 0 14px;
    margin: -1px 0 0;
}

.settings_privacy_row:first-child {
    border-top: none
}

.settings_privacy_row:last-child {
    border-bottom: none
}

.settings_privacy_row.setting_row_selected.setting_row_animated {
    background-color: transparent;
    border-color: transparent
}

.settings_privacy_row.settings_privacy_row_oneline .settings_label {
    padding: 11px 10px 0 0
}

[dir=rtl] .settings_privacy_row.settings_privacy_row_oneline .settings_label {
    padding: 11px 0 0 10px
}

.settings_privacy_row--no-bottom-border {
    border-bottom-width: 0
}

.settings_privacy_row .settings_labeled_text {
    margin-left: 255px;
}

.settings_privacy_row .settings_label {
    width: 245px;
    text-align: left;
    line-height: 18px
}
.settings_list_row:has(select, input:not([type="submit"])) .settings_label {
    padding: 7px 10px 0 0;
}

.settings_row_button_wrap {
    padding-left: 155px;
    margin: 15px 0 25px 0;
}

.settings_save_footer {
    text-align: center;
    margin-top: 20px;
}

.settings_panel .accent-box {
    margin-top: 0;
    margin-inline: 0;
}

/* payments */

.settings_padding {
    padding-block: 20px
}

.voucher_form {
    display: flex;
    justify-content: center;
    align-items: center;
}
.voucher_form .settings_voucher_input {
    margin-right: 10px;
}

.delete_reason_list {
    margin: 10px 0 20px;
}

/* edit page */
.edit_panel {
    padding-block: 20px;
}
.edit_panel .settings_label,.group_settings .settings_label {
    text-align:end;
}
.group_settings .settings_label {
	width: 165px;
	padding: 6px 12px 7px 0;
}
.group_settings .settings_labeled_text {
	margin: 0 0 8px 177px;
	padding-top: 7px;
}
.group_settings .settings_labeled_text:has(textarea,input[type="text"],select) {
    padding-top: 2px;
}

/* followers */
.group_l_row {
	position: relative;
	padding: 15px 0;
}
.group_l_row:not(:last-child) {
	border-bottom: 1px solid var(--border-color);
}
.group_u_bigph_wrap {
	position: relative;
	overflow: hidden;
	width: 80px;
	height: 80px;
	float: left;
	margin-right: 12px;
}
.group_u_photo_img {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	overflow: hidden;
}
.group_u_actions {
	float: right;
	padding-top: 9px;
}
.group_u_info {
	overflow: hidden;
	text-overflow: ellipsis;
	padding-top: 9px;
}
.group_u_desc, .group_u_btns_text {
	padding-top: 6px;
	color: var(--muted-text-color);
}
.group_u_info_row {
	padding-top: 7px;
}

.game_edit {
    width: fit-content;
    margin: 0 auto;
}