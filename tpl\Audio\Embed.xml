<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
    <head>
        <meta http-equiv="Content-Type" content="application/xhtml+xml; charset=utf-8" />
        <link rel="icon">
        <title>{$audio->getName()}</title>
        
        {css "css/main.css"}
        {css "css/audios.css"}
        {script "js/node_modules/dashjs/dist/dash.all.min.js"}
        {script "js/node_modules/jquery/dist/jquery.min.js"}
        {script "js/node_modules/umbrellajs/umbrella.min.js"}
    </head>
    <body id="audioEmbed">
        {include "player.xml", audio => $audio}

        {script "js/al_music.js"}
    </body>
</html>
