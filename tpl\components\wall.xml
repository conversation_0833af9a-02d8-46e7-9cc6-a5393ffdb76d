<div class="wall_module">
    <div>
    <div class="insertThere" id="postz"></div>
    <div id="underHeader">
        <div n:if="$canPost" class="page_block">
            {include "../components/textArea.xml", route => "/wall$owner/makePost", graffiti => true, polls => true, notes => true, hasSource => true, geo => true, docs => true}
        </div>
        {var $tabs = [
            [
                'url' => "/wall{$owner}",
                'title' => 'all_posts',
                'active' => true,
                'id' => 'wall_tab_all',
                'count' => $count
            ],
            [
                'url' => "/wall{$owner}?type=owners",
                'title' => isset($club) ? "clubs_posts" : tr("users_posts", ovk_proc_strtr($oObj->getFirstName(), 20)),
                'active' => false,
                'id' => 'wall_tab_owners',
                'translate' => isset($club) ? true : false,
            ]
        ]}
        {include "../components/page_tabs_header.xml", tabs => $tabs, id => 'wall_top_tabs', white => true}
        <div class="content scroll_container">
            {if sizeof($posts) > 0}
                {foreach $posts as $post}
                    {include "../components/post.xml", post => $post, commentSection => true, onWallOf => true}
                {/foreach}
                {include "../components/paginator.xml", conf => $paginatorConf}
            {else}
				<div class="page_block page_padding">
					{include "../components/content_error.xml", description => tr("no_posts_abstract")}
				</div>
            {/if}
        </div>
    </div>
    </div>
</div>
