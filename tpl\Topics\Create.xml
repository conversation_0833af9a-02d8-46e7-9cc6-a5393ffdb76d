{extends "../@layout.xml"}
{block title}{_new_topic}{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs: [
        ['href' => $club->getURL(), 'title' => $club->getCanonicalName()],
        ['href' => "/board{$club->getId()}", 'title' => tr("discussions")],
        ['title' => tr("new_topic")]
    ]}
    <div class="page_block">
        <form method="POST" enctype="multipart/form-data">
            <div class="settings_panel group_settings settings_padding" style="width: 455px;margin-inline: auto;">
                <div class="settings_list_row">
                    <div class="settings_label">{_title}</div>
                    <div class="settings_labeled_text">
                        <input type="text" name="title" style="width: 100%;" />
                    </div>
                </div>
                <div class="settings_list_row">
                    <div class="settings_label">{_text}</div>
                    <div class="settings_labeled_text">
                        <textarea id="wall-post-input1" name="text" style="width: 100%; resize: none;"></textarea>
                        <div n:if="$club->canBeModifiedBy($thisUser)" class="post-opts">
                            <label>
                                <input type="checkbox" name="as_group" onchange="onWallAsGroupClick(this)" /> {_post_as_group}
                            </label>
                        </div>
                        <div id="post-buttons1">
                            <div class="post-upload">
                                {_attachment}: <span>(unknown)</span>
                            </div>
                            <input type="file" class="postFileSel" id="postFilePic" name="_pic_attachment" accept="image/*" style="display: none;" />
                            <input n:if="!OPENVK_ROOT_CONF['openvk']['preferences']['videos']['disableUploading']" type="file" class="postFileSel" id="postFileVid" name="_vid_attachment" accept="video/*" style="display: none;" />
                            <div>
                                <a id="moreAttachTrigger">
                                    {_attach}
                                </a>

                                <div class="tippy-menu" id="moreAttachTooltip">
                                    <a class="attach_photo" href="javascript:void(document.querySelector('#post-buttons1 input[name=_pic_attachment]').click());">
                                        <div class="post-attach-menu__icon"></div>
                                        {_attach_photo}
                                    </a>
                                    <a class="attach_video" n:if="!OPENVK_ROOT_CONF['openvk']['preferences']['videos']['disableUploading']" href="javascript:void(document.querySelector('#post-buttons1 input[name=_vid_attachment]').click());">
                                        <div class="post-attach-menu__icon"></div>
                                        {_attach_video}
                                    </a>
                                    <a class="attach_graffiti" n:if="$graffiti ?? false" href="javascript:initGraffiti(1);">
                                        <div class="post-attach-menu__icon"></div>
                                        {_draw_graffiti}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="settings_save_footer">
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                    <input type="submit" value="{_create_topic}" class="button" />
                </div>
            </div>
            <input type="hidden" name="hash" value="{$csrfToken}" />
        </form>
    </div>

    <script>
        $(document).ready(() => {
            u("#post-buttons1 .postFileSel").on("change", function() {
                handleUpload.bind(this, 1)();
            });

            setupWallPostInputHandlers(1);
        });
    </script>
{/block}
