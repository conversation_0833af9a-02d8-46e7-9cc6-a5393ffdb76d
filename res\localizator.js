let locales = {
	'en': {
		"language": "Language",
		"currentlyplaying": "Currently playing: ",
		"loadmore": "Load more",
		"graffitiflushhistory": "Clear",
		"graffitibackhistory": "Cancel",
		"graffiticolor": "Color:",
		"graffitiopacity": "Opacity:",
		"graffitithickness": "Thickness:",
		"graffitiswitch": "Use graffiti from VKontakte",
		"ovkhat": `Use OpenVK logo in header`,
		"httpwarn": `This OpenVK instance uses the outdated <b>http</b> protocol.<br>Please, migrate to <b>https</b>.`,
		"httpwarnovk": `You are using an insecure protocol: <b>http</b>. Please always use <b>https</b>.<br><a href='https://{url}/'>Switch to https »</a>`,
		"loginPromo": `OpenVK for mobile devices`,
		"loginPromoInfo": `Install the official OpenVK app and stay up to date with your friends' news, wherever you are.`,
		"loginPromoAndroid": "For Android",
		"loginPromoWp": "For WP",
		"loginPromoIos": "For iOS",
		"newUserQuestionMark": `New to OpenVK?`,
		"newUserSubhead": "Instant registration",
		"external": "External",
		"left_edge": "Left edge:",
		"right_edge": "Right edge:",
		"back_to_page": "back to page",
		"clear_playlist": "Clear playlist",
		"users_posts": "Posts from user",
		"profile_recommendations": "Show recommendations on filling out your profile",
	    "guest_actions": "<a href='/register'>Register now</a> to stay connected with $1 and other people, or <a href='/'>log in</a> if you already have an account.",
		"recommended_groups": "Recommended groups",
		"show_less": "Show less",
		"deleted_attachment": "This post contains deleted attachment(s) and doesn't have any text.",
		"deleted_attachment_2": "As such, it can't be displayed.",
		"dark_mode": "Dark mode"
	},
	'ru': {
		"language": "Язык",
		"currentlyplaying": "Сейчас воспроизводится: ",
		"loadmore": "Загрузить ещё",
		"graffitiflushhistory": "Очистить",
		"graffitibackhistory": "Отменить",
		"graffiticolor": "Цвет:",
		"graffitiopacity": "Интенсивность:",
		"graffitithickness": "Толщина:",
		"graffitiswitch": "Использовать граффити из ВКонтакте",
		"ovkhat": `Использовать лого OpenVK в шапочке`,
		"httpwarn": `Этот инстанс OpenVK использует устаревший протокол <b>http</b>.<br>Пожалуйста, смените его на <b>https</b>.`,
		"httpwarnovk": `Вы используете небезопасный протокол <b>http</b>. Пожалуйста, всегда используйте <b>https</b>.<br><a href='https://{url}/'>Перейти на https »</a>`,
		"loginPromo": `OpenVK для мобильных устройств`,
		"loginPromoInfo": `Установите официальное приложение OpenVK и оставайтесь в курсе новостей друзей, где бы вы ни находились.`,
		"loginPromoAndroid": "Для Android",
		"loginPromoWp": "Для WP",
		"loginPromoIos": "Для iOS",
		"newUserQuestionMark": `Впервые в OpenVK?`,
		"newUserSubhead": "Мгновенная регистрация",
		"external": "Внешний",
		"left_edge": "Левая граница:",
		"right_edge": "Правая граница:",
		"back_to_page": "вернуться к странице",
		"clear_playlist": "Очистить плейлист",
		"users_posts": "Записи от пользователя",
		"profile_recommendations": "Показывать рекомендации по заполнению профиля",
	    "guest_actions": "<a href='/register'>Зарегистрируйтесь</a>, чтобы оставаться на связи с $1 и другими людьми, или <a href='/'>войдите</a>, если у вас уже есть аккаунт.",
		"recommended_groups": "Рекомендуемые сообщества",
		"show_less": "Показать меньше",
		"deleted_attachment": "В этой записи содержится удалённое вложение и нет текста.",
		"deleted_attachment_2": "Поэтому она не может быть отображена.",
		"dark_mode": "Тёмная тема"
	},
	'uk': {
		"language": "Мова",
		"currentlyplaying": "Зараз грає: ",
		"loadmore": "Завантажити ще",
		"graffitiflushhistory": "Очистити",
		"graffitibackhistory": "Скасувати",
		"graffiticolor": "Колір:",
		"graffitiopacity": "Непрозорість:",
		"graffitithickness": "Товщина:",
		"graffitiswitch": "Використовувати графіті з ВКонтакте",
		"ovkhat": "Використовувати логотип OpenVK у шапці",
		"httpwarn": "Цей інстанс OpenVK використовує застарілий протокол <b>HTTP</b>.<br>Будь ласка, перейдіть на <b>HTTPS</b>.",
		"httpwarnovk": "Ви використовуєте незахищений протокол <b>HTTP</b>. Завжди використовуйте <b>HTTPS</b>.<br><a href='https://{url}/'>Перейти на HTTPS »</a>",
		"loginPromo": "OpenVK для мобільних пристроїв",
		"loginPromoInfo": "Встановіть офіційний застосунок OpenVK і залишайтеся в курсі новин друзів, де б ви не були.",
		"loginPromoAndroid": "Для Android",
		"loginPromoWp": "Для WP",
		"loginPromoIos": "Для iOS",
		"newUserQuestionMark": "Вперше в OpenVK?",
		"newUserSubhead": "Миттєва реєстрація",
		"external": "Зовнішнє",
		"left_edge": "Лівий край:",
		"right_edge": "Правий край:",
		"back_to_page": "Повернутися до сторінки",
		"clear_playlist": "Очистити список відтворення",
		"users_posts": "Дописи користувача",
		"profile_recommendations": "Показувати рекомендації щодо заповнення профілю",
		"guest_actions": "<a href='/register'>Зареєструйтеся</a>, щоб залишатися на зв'язку з $1 та іншими, або <a href='/'>увійдіть</a>, якщо у вас вже є обліковий запис.",
		"recommended_groups": "Рекомендовані спільноти",
		"deleted_attachment": "Цей допис містить видалене вкладення та не має тексту.",
		"deleted_attachment_2": "Тому він не може бути відображений.",
		"dark_mode": "Темний режим"
	}
}

window.vkifylocalize = function(langcode) {
	if (!(langcode in locales)) {
		try {
		fetch(`/themepack/vkify16/3.3.0.4/resource/langs/${langcode}.json`)
			.then(response => {
				if (!response.ok) {
					langcode = 'en';
					patchpage(langcode);
					return;
				}
				return response.json();
			})
			.then(data => {
				if (data) {
					locales[langcode] = data;
					patchpage(langcode);
				}
			})
		} catch(error) {
			langcode = 'en';
			patchpage(langcode);
		}
	} else {
		patchpage(langcode);
	}
}

window.processVkifyLocElement = function(element) {
	if (!window.vkifylang) return;

	const locName = element.getAttribute('name');
	if (locName && window.vkifylang[locName]) {
		let translatedText = window.vkifylang[locName];

		const args = element.getAttribute('args');
		if (args) {
			const argArray = args.split(',').map(arg => arg.trim());
			argArray.forEach((arg, index) => {
				const placeholder = '$' + (index + 1);
				translatedText = translatedText.replace(new RegExp('\\' + placeholder, 'g'), arg);
			});
		}

		element.outerHTML = translatedText;
	}
}

window.processVkifyLocTags = function() {
	if (!window.vkifylang) return;

	document.querySelectorAll('vkifyloc').forEach(element => {
		window.processVkifyLocElement(element);
	});
}

function patchpage(langcode) {
	window.vkifylang = locales[langcode];
	window.processVkifyLocTags();

	if (location.protocol.includes('http:') && !location.host.includes('localhost')) {
		if (location.host.includes('openvk.xyz')) {
			showBlueWarning(window.vkifylang.httpwarnovk);
		} else {
			showBlueWarning(window.vkifylang.httpwarn);
		}
	}
}

window.addEventListener('DOMContentLoaded', () => {
	if (!window.vkifylang) return;

	const observer = new MutationObserver((mutations) => {
		mutations.forEach(mutation => {
			mutation.addedNodes.forEach(node => {
				if (node.nodeType === Node.ELEMENT_NODE) {
					const locTags = node.querySelectorAll('vkifyloc');
					if (locTags.length > 0) {
						locTags.forEach(element => {
							window.processVkifyLocElement(element);
						});
					}

					if (node.nodeName === 'VKIFYLOC') {
						window.processVkifyLocElement(node);
					}
				}
			});
		});
	});

	observer.observe(document.body, {
		childList: true,
		subtree: true
	});
});
