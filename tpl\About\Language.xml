{extends "../@layout.xml"}
{block title}{_select_language}{/block}

{block header}
    {_select_language}
{/block}

{block content}
    <style>
        .navigation-lang {
            display: grid;
            grid-gap: 10px;
            grid-template-columns: repeat(5, 1fr);
        }

        .navigation-lang .link_new {
            display: inline-block;
            padding: 20px 10px 5px 10px;
            text-decoration: none;
            border-top: 1px solid #fff;
            color: #000;
            border-bottom: 0;
            border-left: 0;
            border-right: 0;
            text-align: center;
            font-size: 11px;
            cursor: pointer;
            background: none;
            margin-bottom: 1px;
        }

        .navigation-lang .link_new:hover {
            background-color: #E4E4E4;
            border-top: 1px solid #CCCCCC;
        }
    </style>

    <div class="navigation-lang">
        {foreach $languages as $language}
            {var $result = preg_match("/(.+)\((.+)\)/", $language['native_name'], $name)}

            <a href="language?lg={$language['code']}&hash={urlencode($csrfToken)}" class="link_new" rel="nofollow">
                <center><img src="/assets/packages/static/openvk/img/flags/{$language['flag']}.gif" alt="{$language['native_name']}"></center>
                <br>
                {if $result == 1}
                    {$name[1]}
                    <br>
                    <small>{$name[2]}</small>
                {else}
                    {$language['native_name']}
                {/if}
            </a>
        {/foreach}
    </div>
{/block}
