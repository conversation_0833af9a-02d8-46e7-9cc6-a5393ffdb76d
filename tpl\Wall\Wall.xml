{extends "../@layout.xml"}
{block title}{_wall}{/block}

{block content}
{var $menuItems = [
    [
        'url' => '/wall' . $owner . '?type=owners',
        'title' => isset($club) ? "clubs_posts" : tr("users_posts", ovk_proc_strtr($oObj->getFirstName(), 20)),
        'translate' => isset($club) ? true : false,
        'active' => $type == 'owners'
    ],
    [
        'url' => '/wall' . $owner,
        'title' => 'all_posts',
        'active' => $type == 'all'
    ],
    [
        'url' => "/notes" . $owner,
        'title' => 'notes',
        'active' => false,
        'condition' => $owner > 0
    ]
]}

    <div class="wide_column_left">
        <div class="narrow_column_wrap">
            <div class="narrow_column">
                {include "../components/ui_rmenu.xml", items => $menuItems, ownblock => true}
            </div>
        </div>
        <div class="wide_column_wrap">
            <div class="wide_column">
                <div n:if="$canPost && $type == 'all'" class="page_block">
                    {include "../components/textArea.xml", route => "/wall$owner/makePost", graffiti => true, polls => true, notes => true, hasSource => true, geo => true}
                </div>
                
                <div class="content scroll_container">
                    {if sizeof($posts) > 0}
                        {foreach $posts as $post}
                            {include "../components/post.xml", post => $post, commentSection => true}
                        {/foreach}
                        {include "../components/paginator.xml", conf => $paginatorConf}
                    {else}
                        <div class="page_block module_body" style="border-top: 0px;">
                            {_no_posts_abstract}
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
{/block}
