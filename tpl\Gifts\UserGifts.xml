{extends "../@listView.xml"}
{block title}
    {tr("users_gifts", $user->getFirstName())}
{/block}

{block header}
    <a href="{$user->getURL()}">{$user->getCanonicalName()}</a> »
    {_gifts}
{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block link|strip|stripHtml}
    javascript:false
{/block}

{block preview}
    <img src="{$x->gift->getImage(2)}" width="75" alt="{_gift}" loading=lazy />
{/block}

{block name}
    {_gift}
{/block}

{block description}
    <table class="ugc-table" n:if="$hideInfo ? (!$x->anon || $x->sender->getId() === $thisUser->getId()) : true">
        <tbody>
            <tr>
                <td><span class="nobold">{_sender}: </span></td>
                <td>
                    <a href="{$x->sender->getURL()}">
                        {$x->sender->getFullName()}
                    </a>
                </td>
            </tr>
            <tr n:if="!empty($x->caption)">
                <td><span class="nobold">{_comment}: </span></td>
                <td>{$x->caption}</td>
            </tr>
        </tbody>
    </table>
{/block}