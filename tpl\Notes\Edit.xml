{extends "../@layout.xml"}

{block title}{_edit_note}{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs: [
        ['href' => $thisUser->getURL(), 'title' => $thisUser->getCanonicalName()],
        ['href' => "/notes{$thisUser->getId()}", 'title' => tr("notes")],
        ['href' => "/note{$thisUser->getId()}_{$note->getVirtualId()}", 'title' => $note->getName()],
        ['title' => tr("edit_note")]
    ]}
    <div class="page_block page_padding">
        <form id="noteFactory" method="POST">
            <input type="text" name="name" placeholder="{_name_note}" value="{$note->getName()}" />
            <br/><br/>
            <textarea name="html" style="display:none;"></textarea>
            <div id="editor" style="height:50vh;border:1px solid var(--border-color)"></div>
            
            <p><i><a href="/kb/notes">{_something}</a> {_supports_xhtml}</i></p>
            
            <input type="hidden" name="hash" value="{$csrfToken}" />
            <button class="button">{_save}</button>
            &nbsp;
            <a href="/note{$note->getOwner()->getId()}_{$note->getVirtualId()}" class="button button_gray">{_cancel}</a>
        </form>
    </div>
    
    {script "js/node_modules/monaco-editor/min/vs/loader.js"}
    {script "js/node_modules/requirejs/bin/r.js"}
    <script>
        require.config({
            paths: {
                'vs': '/assets/packages/static/openvk/js/node_modules/monaco-editor/min/vs' 
            }
        });
        require(['vs/editor/editor.main'], function() {
            window._editor = monaco.editor.create(document.getElementById('editor'), {
                value: {$note->getSource()},
                lineNumbers: "off",
                language: "html"
            });
        });
        
        document.querySelector("#noteFactory").addEventListener("submit", function() {
            document.querySelector("textarea").value = window._editor.getValue();
        });
    </script>
{/block}
