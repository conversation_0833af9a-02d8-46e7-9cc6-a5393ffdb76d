{extends "../@layout.xml"}
{if $mode === 'backdrop'}
    {var $backdrops = $user->getBackDropPictureURLs()}
{/if}



{block title}{_edit_page}{/block}

{block content}

    {var $isMain      = $mode === 'main'}
    {var $isContacts  = $mode === 'contacts'}
    {var $isInterests = $mode === 'interests'}
    {var $isAdditional = $mode === 'additional'}
    {var $isAvatar    = $mode === 'avatar'}
    {var $isBackDrop  = $mode === 'backdrop'}

    {var $menuItems = [
        [
            'url' => '/edit',
            'title' => 'main',
            'active' => $isMain
        ],
        [
            'url' => '/edit?act=contacts',
            'title' => 'contacts',
            'active' => $isContacts
        ],
        [
            'url' => '/edit?act=interests',
            'title' => 'interests',
            'active' => $isInterests
        ],
        [
            'url' => '/edit?act=additional',
            'title' => 'additional',
            'active' => $isAdditional
        ],
        [
            'url' => '/edit?act=avatar',
            'title' => 'avatar',
            'active' => $isAvatar
        ],
        [
            'url' => '/edit?act=backdrop',
            'title' => 'backdrop_short',
            'active' => $isBackDrop
        ]
    ]}
    

    <div n:if="$user->hasPendingNumberChange()" class="msg">
        <b>Подтверждение номера телефона</b><br/>
        Введите код для подтверждения смены номера: <a href="/edit/verify_phone">ввести код</a>.
    </div>

    
    <div class="wide_column_left">
        <div class="narrow_column_wrap">
            <div class="narrow_column">
                {include "../components/ui_rmenu.xml", items => $menuItems, ownblock => true}
            </div>
        </div>
        <div class="wide_column_wrap">
            <div class="wide_column">
            {if $isMain}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "main_information"}
                    <form action="/edit?act=main" method="POST" enctype="multipart/form-data">
                        <div class="settings_panel edit_panel">
                            <div class="settings_list_row">
                                <div class="settings_label">{_name}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="first_name" value="{$user->getFirstName(true)}" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_surname}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="last_name" value="{$user->getLastName(true)}" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_nickname}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="pseudo" value="{$user->getPseudo()}" />
                                </div>
                            </div>
                            {if OPENVK_ROOT_CONF['openvk']['credentials']['smsc']['enable']}
                            <div class="settings_list_row">
                                <div class="settings_label">{_phone}</div>
                                <div class="settings_labeled_text">
                                    <input type="phone" name="phone" value="{$user->getPhone()}" />
                                </div>
                            </div>
                            {/if}
                            <div class="settings_list_row">
                                <div class="settings_label">{_status}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="status" value="{$user->getStatus()}" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_hometown}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="hometown" value="{$user->getHometown()}" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_relationship}</div>
                                <div class="settings_labeled_text">
                                    <select name="marialstatus" onChange="toggleMaritalStatus(this)">
                                        <option n:foreach="range(0, 8) as $i" n:attr="selected => ($user->getMaritalStatus() == $i)" value="{$i}">
                                            {if $user->isFemale()}
                                                {var $str = "relationship_$i"}
                                                {if tr($str . "_fem") == ("@$str" . "_fem")}
                                                    {_$str}
                                                {else}
                                                    {tr($str . "_fem")}
                                                {/if}
                                            {else}
                                                {_"relationship_$i"}
                                            {/if}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div id="maritalstatus-user" class="settings_list_row"
                                n:attr="style => $user->getMaritalStatusUser() || ($user->getMaritalStatus() && !in_array($user->getMaritalStatus(), [0, 1, 8])) ? '' : 'display: none;'">
                                <div class="settings_label"></div>
                                <div class="settings_labeled_text">
                                    <input type="text" placeholder="{_page_address}" name="maritalstatus-user"
                                        n:attr="value => $user->getMaritalStatusUser() ? $user->getMaritalStatusUser()->getId() : ''" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_politViews}</div>
                                <div class="settings_labeled_text">
                                    <select name="politViews">
                                        <option n:foreach="range(0, 9) as $i" n:attr="selected => ($user->getPoliticalViews() == $i)" value="{$i}">
                                            {tr("politViews_" . $i)}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_pronouns}</div>
                                <div class="settings_labeled_text">
                                    <select name="pronouns">
                                        <option value="0" {if $user->getPronouns() == 0}selected{/if}>{_male}</option>
                                        <option value="1" {if $user->getPronouns() == 1}selected{/if}>{_female}</option>
                                        <option value="2" {if $user->getPronouns() == 2}selected{/if}>{_neutral}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_birth_date}</div>
                                <div class="settings_labeled_text">
                                    <input max={date('Y-m-d')} name="birthday" value={is_null($user->getBirthday()) ? NULL : $user->getBirthday()->format('%Y-%m-%d')} type="date" style="margin-bottom: 7px;" />
                                    <select name="birthday_privacy">
                                        <option value="0" {if $user->getBirthdayPrivacy() == 0}selected{/if}>{_show_my_birthday}</option>
                                        <option value="1" {if $user->getBirthdayPrivacy() == 1}selected{/if}>{_show_only_month_and_day}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label"></div>
                                <div class="settings_labeled_text">
                                    <label><input type="checkbox" name="broadcast_music" n:attr="checked => $user->isBroadcastEnabled()">{_broadcast_audio}</label>
                                </div>
                            </div>
                            <div class="settings_save_footer">
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <input type="submit" value="{_save}" class="button" />
                            </div>
                        </div>
                    </form>
                </div>

            {elseif $isContacts}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "contact_information"}
                    <form action="/edit?act=contacts" method="POST" enctype="multipart/form-data">
                        <div class="settings_panel edit_panel">
                            <div class="settings_list_row">
                                <div class="settings_label">{_email}</div>
                                <div class="settings_labeled_text">
                                    <input type="email" name="email_contact" value="{$user->getContactEmail()}" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_telegram}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="telegram" value="{$user->getTelegram()}" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_personal_website}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="website" value="{$user->getWebsite()}" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_city}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="city" value="{$user->getCity()}" />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_address}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="address" value="{$user->getPhysicalAddress()}" />
                                </div>
                            </div>
                            <div class="settings_save_footer">
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <input type="submit" value="{_save}" class="button" />
                            </div>
                        </div>
                    </form>
                </div>
            {elseif $isInterests}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "personal_information"}
                    <form action="/edit?act=interests" method="POST" enctype="multipart/form-data">
                        <div class="settings_panel edit_panel">
                            <div class="settings_list_row">
                                <div class="settings_label">{_interests}</div>
                                <div class="settings_labeled_text">
                                    <textarea type="text" name="interests">{$user->getInterests()}</textarea>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_favorite_music}</div>
                                <div class="settings_labeled_text">
                                    <textarea type="text" name="fav_music">{$user->getFavoriteMusic()}</textarea>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_favorite_films}</div>
                                <div class="settings_labeled_text">
                                    <textarea type="text" name="fav_films">{$user->getFavoriteFilms()}</textarea>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_favorite_shows}</div>
                                <div class="settings_labeled_text">
                                    <textarea type="text" name="fav_shows">{$user->getFavoriteShows()}</textarea>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_favorite_books}</div>
                                <div class="settings_labeled_text">
                                    <textarea type="text" name="fav_books">{$user->getFavoriteBooks()}</textarea>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_favorite_quotes}</div>
                                <div class="settings_labeled_text">
                                    <textarea type="text" name="fav_quote">{$user->getFavoriteQuote()}</textarea>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_favorite_games}</div>
                                <div class="settings_labeled_text">
                                    <textarea type="text" name="fav_games">{$user->getFavoriteGames()}</textarea>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_information_about}</div>
                                <div class="settings_labeled_text">
                                    <textarea type="text" name="about">{$user->getDescription()}</textarea>
                                </div>
                            </div>
                            <div class="settings_save_footer">
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <input type="submit" value="{_save}" class="button" />
                            </div>
                        </div>
                    </form>
                </div>

            {elseif $isAvatar}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "profile_picture"}
                    <form action="/al_avatars" method="POST" enctype="multipart/form-data">
                        <div class="settings_panel edit_panel">
                            <div class="settings_list_row">
                                <div class="settings_label">{_picture}</div>
                                <div class="settings_labeled_text">
                                    <label class="button" style="">{_browse}
                                        <input type="file" id="blob" name="blob" style="display: none;" onchange="filename.innerHTML=blob.files[0].name" />
                                    </label>
                                    <div id="filename" style="margin-top: 10px;"></div>
                                </div>
                            </div>
                            <div class="settings_save_footer">
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <input type="submit" value="{_save}" class="button" />
                            </div>
                        </div>
                    </form>
                </div>

            {elseif $isBackDrop}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "backdrop"}
                        <div class="settings_block_msg">
                            <p>{_backdrop_desc}</p>
                            <p>{_backdrop_warn}</p>
                            <p>{_backdrop_about_adding}</p>
                        </div>
                        <form method="POST" enctype="multipart/form-data">
                    <div class="settings_panel edit_panel">
                            <div class="settings_list_row">
                                <div class="settings_label"><vkifyloc name="left_edge"></vkifyloc></div>
                                <div class="settings_labeled_text">
                                    <label class="button" style="">{_browse}<input type="file" accept="image/*" name="backdrop1" style="display: none;"></label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label"><vkifyloc name="right_edge"></vkifyloc></div>
                                <div class="settings_labeled_text">
                                    <label class="button" style="">{_browse}<input type="file" accept="image/*" name="backdrop2" style="display: none;"></label>
                                </div>
                            </div>
                            <div class="settings_save_footer">
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <center>
                                    <button name="subact" value="save" class="button">{_backdrop_save}</button>
                                    <button name="subact" value="remove" class="button button_gray">{_backdrop_remove}</button>
                                </center>
                            </div>
                        </div>
                    </form>
                </div>

            {elseif $isAdditional}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "additional_information"}
                    <div class="settings_panel edit_panel settings_padding">
                        {var $f_iterator = 0}
                        <div>{tr("additional_fields_description", ovkGetQuirk("users.max-fields"))}</div>
                        <form id="additional_fields_form" method="POST" enctype="multipart/form-data">
                            <div class="edit_field_container_inserts">
                                <table data-iterator="{$f_iterator}" class="outline_table edit_field_container_item" width="80%" border="0" align="center" n:foreach="$thisUser->getAdditionalFields() as $field">
                                    <tbody>
                                        <tr>
                                            <td width="150">
                                                {_additional_field_name}
                                            </td>
                                            <td>
                                                <input name="name_{$f_iterator}" maxlength="50" type="text" value="{$field->getName(false)}">
                                            </td>
                                            <td>
                                                <div id="small_remove_button"></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td valign="top">
                                                {_additional_field_text}
                                            </td>
                                            <td>
                                                <textarea name="text_{$f_iterator}" maxlength="1000">{$field->getContent()}</textarea>
                                            </td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {_additional_field_place}
                                            </td>
                                            <td>
                                                <select name="place_{$f_iterator}">
                                                    <option value="0" n:attr="selected => $field->isContact()">{_additional_field_place_contacts}</option>
                                                    <option value="1" n:attr="selected => !$field->isContact()">{_additional_field_place_interests}</option>
                                                </select>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tbody>

                                    {php $f_iterator += 1}
                                </table>
                            </div>

                            <div class="flex_column_center_gap5px settings_save_footer">
                                <input type="button" id="additional_field_append" value="{_add}" class="button" />
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <input type="submit" value="{_save}" class="button" />
                            </div>
                        </form>
                    </div>
                </div>
            {/if}
        </div>
    </div>
{/block}
