{extends "../@layout.xml"}

{block title}{$note->getName()}{/block}

{block content}
    {var $author = $note->getOwner()}
    {var $menuItems = [
        [
            'url' => "/wall" . $author->getId() . "?type=owners",
            'title' => tr("users_posts", ovk_proc_strtr($author->getFirstName(), 20)),
            'translate' => false,
            'active' => false
        ],
        [
            'url' => "/wall" . $author->getId(),
            'title' => 'all_posts',
            'active' => false
        ],
        [
            'url' => "/notes" . $author->getId(),
            'title' => 'notes',
            'active' => false
        ],
        [
            'url' => "javascript:void(0)",
            'title' => 'note',
            'active' => true
        ]
    ]}

    <div class="wide_column_left">
        <div class="narrow_column_wrap">
            <div class="narrow_column">
                {include "../components/ui_rmenu.xml", items => $menuItems}
            </div>
        </div>
        <div class="wide_column_wrap">
            <div class="wide_column">
                {include "../components/page_crumb_header.xml", crumbs: [
                    ['href' => $author->getURL(), 'title' => $author->getCanonicalName()],
                    ['href' => "/notes{$author->getId()}", 'title' => tr("notes")],
                    ['title' => $note->getName()]
                ]}

                {include "../components/post/notepost.xml", note => $note, forceNoCommentsLink => true}
                {include "../components/comments.xml",
                    comments => $comments,
                    count => $cCount,
                    page => $cPage,
                    model => "notes",
                    parent => $note,
                    showTitle => false}
            </div>
        </div>
    </div>
{/block}
