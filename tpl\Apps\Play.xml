{extends "../@layout.xml"}
{var $canReport = $owner->getId() !== $thisUser->getId()}

{var $layoutWidth = "815px"}
{var $pageWidth = "650px"}

{block title}
    {$name}
{/block}

{block header}
    {$name}
    <a style="float: right;" onClick="reportApp({$id})" n:if="$canReport ?? false">{_report}</a>
{/block}

{block content}
    <style>
    body {
        --layout-width: 815px;
    }
    .page_body {
        width: 650px
    }
    </style>
    <div class="page_block">
        <iframe id="appFrame" referrerpolicy="unsafe-url" frameBorder="0" src="{$url}" height="600" width="100%"
            sandbox="allow-scripts allow-same-origin allow-pointer-lock allow-forms allow-downloads-without-user-activation"></iframe>

            <div n:if="!is_null($news)" class="page_padding">
                <h4>{$news->getName()}</h4>
                <div id="app_news_container" style="margin-bottom: 0">
                    {$news->getText()|noescape}
                </div>
            </div>
            <div class="settings_block_footer">
                {_app_dev}: <a href="{$owner->getURL()}">{$owner->getFullName()}</a>
            </div>
    </div>

    <script>
        window.appId     = {$id};
        window.appTitle  = {$name};
        window.appPerms  = {$perms};
        window.appOrigin = {$origin};
    </script>

    {script "js/al_games.js"}
{/block}
