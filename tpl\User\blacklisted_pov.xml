{extends "../@layout.xml"}
{block title}{$user->getCanonicalName()}{/block}

{block content}
    <div class="wide_column_right">
        <div class="narrow_column_wrap">
            <div class="narrow_column">
				<div class="page_block photo_block">
                    <div class="avatar_block">
						<div class="avatar_block_inner">
                            <img src="{$user->getAvatarUrl('normal')}"
                                alt="{$user->getCanonicalName()}"
                                style="width: 100%; image-rendering: -webkit-optimize-contrast;" />
                        </div>
                    </div>
                    <div class="profile_actions clear_fix" n:if="isset($thisUser)">
                        <a n:if="!$blacklist_status" id="_bl_toggler" data-val="0" data-id="{$user->getRealId()}" class="button button_gray button_wide">{_bl_remove}</a>
                        <a class="button button_gray button_wide" href="javascript:reportUser({$user->getId()})">{_report}</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="wide_column_wrap">
            <div class="wide_column">
                <div class="page_block page_info_wrap">
						<div class="accountInfo clearFix">
							<div class="page_top">
                                <h2 class="page_name">{$user->getFullName()}
                                    <a class="page_verified" n:if="$user->isVerified()" href="/verify"></a>
                                </h2>
                                <div class="page_status" style="color: #AAA;">{_closed_page}</div>
                            </div>
                        </div>
                        <div class="msg msg_yellow">
                            {tr("you_blacklisted", $user->getMorphedName("genitive", false))}.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}
