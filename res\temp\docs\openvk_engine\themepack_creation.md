# How to create a Themepack?

Create a directory, the name of which should contain only Latin letters and numbers, and create a file there `theme.yml`, and fill it with the following content:

```yaml
id: vk2007
version: "*******"
openvk_version: 0
enabled: 1
metadata:
    name:
        _: "V Kontakte 2007"
        en: "V Kontakte 2007"
        ru: "В Контакте 2007"
    author: "Veselcraft"
    description: "V Kontakte-stylized theme by 2007 era"
```

**Where:**

`id` is the name of the folder

`version` - version of the theme

`openvk_version` - version OpenVK *(it is necessary to leave the value 0)*

`metadata`:

* `name` - the name of the theme for the end user. Inside it you can leave names for different languages. `_` (underscore) - for all languages.

Next, in `stylesheet.css` you can insert any CSS code, with which you can change the elements of the site. If you need additional pictures or resources, just create a `res` folder, and access the resources via the `/themepack/{directory name}/{theme version}/resource/{resource}` path.

To support the New Year's mood, which turns on automatically from December 1st to January 15th, create the file `xmas.css` in the `res` folder, and make the necessary changes.

**After all, the directory hierarchy should look like this:**

```
vk2007:
- res
  - {resources}
- stylesheet.css
- theme.yml
```

Lastly, put your theme to the "themepacks" folder in OpenVK root directory, and you're done!

**Templates override**

You can put custom templates in your themepack. For this, add to `theme.yml`:
```yaml
    override_templates: true
    inherits_master: false
```

After this, copy `openvk/Web/Presenters/templates` dir into themepack folder and rename it to `tpl`. Now, engine will load templates from your themepack.
