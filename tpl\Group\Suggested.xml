{extends "../@layout.xml"}

{block title}{_suggested} {$club->getCanonicalName()}{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs => [
        ['title' => $club->getCanonicalName(), 'href' => $club->getURL()],
        ['title' => ($type == "my" ? tr("suggested_posts_by_you") : tr("suggested_posts_by_everyone")) . " ($count)"]
    ]}

        {if $count < 1}
            <div class="page_block">
                {include "../components/error.xml", title => "", description => $type == "my" ? tr("no_suggested_posts_by_you") : tr("no_suggested_posts_by_people")}
            </div>
        {else}
            <div id="postz" class="infContainer scroll_container">
                {var $microblog = $thisUser->hasMicroblogEnabled()}
                <div class="infObj scroll_node page_block" n:foreach="$posts as $post">
                    {if $microblog}
                        {include "../components/post/microblogpost.xml", post => $post, commentSection => false, suggestion => true, forceNoCommentsLink => true, forceNoPinLink => true, forceNoLike => true, forceNoShareLink => true, forceNoDeleteLink => false}
                    {else}
                        {include "../components/post/oldpost.xml", post => $post, commentSection => false, suggestion => true, forceNoCommentsLink => true, forceNoPinLink => true, forceNoLike => true, forceNoShareLink => true, forceNoDeleteLink => false}
                    {/if}
                </div>

                {include "../components/paginator.xml", conf => (object) [
                    "page"     => $page,
                    "count"    => $count,
                    "amount"   => sizeof($posts),
                    "perPage"  => OPENVK_DEFAULT_PER_PAGE,
                    "atBottom" => true,
                ]}
            </div>
        {/if}
{/block}
