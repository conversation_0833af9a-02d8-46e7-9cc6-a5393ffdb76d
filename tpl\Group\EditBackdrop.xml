{extends "../@layout.xml"}
{var $backdrops = $club->getBackDropPictureURLs()}

{block title}{_edit_group}{/block}

{block content}
    <div class="wide_column_left">
        <div class="wide_column_left">
            <div class="narrow_column_wrap">
                <div class="narrow_column">
                    {var $menuItems = [
                        [
                            'url' => "/club{$club->getId()}/edit",
                            'title' => 'main',
                            'active' => false
                        ],
                        [
                            'url' => "/club{$club->getId()}/backdrop",
                            'title' => 'backdrop_short',
                            'active' => true
                        ],
                        [
                            'url' => "/club{$club->getId()}/followers",
                            'title' => 'followers',
                            'active' => false
                        ],
                        [
                            'url' => "/club{$club->getId()}/stats",
                            'title' => 'statistics',
                            'active' => false
                        ]
                    ]}
                    {var $ownblockData = [
                        'url' => "/club" . $club->getId(),
                        'name' => $club->getName(),
                        'img' => $club->getAvatarURL()
                    ]}
                    {include "../components/ui_rmenu.xml", items => $menuItems, ownblock => "club"}
                </div>
            </div>
            <div class="wide_column_wrap">
                <div class="wide_column">
                    <div class="page_block">
                        {include "../components/page_block_header.xml", title => "backdrop"}
                        <div class="settings_block_msg">
                            <p>{_backdrop_desc}</p>
                            <p>{_backdrop_warn}</p>
                            <p>{_backdrop_about_adding}</p>
                        </div>
                        <form method="POST" enctype="multipart/form-data">
                    <div class="settings_panel edit_panel">
                            <div class="settings_list_row">
                                <div class="settings_label"><vkifyloc name="left_edge"></vkifyloc></div>
                                <div class="settings_labeled_text">
                                    <label class="button" style="">{_browse}<input type="file" accept="image/*" name="backdrop1" style="display: none;"></label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label"><vkifyloc name="right_edge"></vkifyloc></div>
                                <div class="settings_labeled_text">
                                    <label class="button" style="">{_browse}<input type="file" accept="image/*" name="backdrop2" style="display: none;"></label>
                                </div>
                            </div>
                            <div class="settings_save_footer">
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <center>
                                    <button name="subact" value="save" class="button">{_backdrop_save}</button>
                                    <button name="subact" value="remove" class="button button_gray">{_backdrop_remove}</button>
                                </center>
                            </div>
                        </div>
                    </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}
