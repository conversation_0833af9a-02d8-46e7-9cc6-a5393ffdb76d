{extends "../@layout.xml"}
{block title}{_invite}{/block}

{block content}
    {var $menuItems = [
        [
            'url' => '/friends' . $thisUser->getId(),
            'title' => 'all_friends',
            'active' => false,
            'extraItem' => [
                'content' => $act === 'friends' ? $count : ''
            ]
        ],
        [
            'url' => '/friends' . $thisUser->getId() . '?act=online',
            'title' => 'online',
            'active' => false,
            'extraItem' => [
                'content' => $act === 'online' ? $count : ''
            ]
        ],
        [
            'url' => '/friends' . $thisUser->getId() . '?act=incoming',
            'title' => 'req',
            'active' => false,
            'extraItem' => [
                'content' => ($act === 'incoming' || $act === 'followers' || $act === 'outcoming') ? $count : ''
            ]
        ],
        [
            'isSeparator' => true
        ],
        [
            'url' => '/invite',
            'title' => 'invite',
            'active' => true
        ]
    ]}
    <div class="wide_column_left">
        <div class="narrow_column_wrap">
            <div class="narrow_column">
                {include "../components/ui_rmenu.xml", items => $menuItems, ownblock => 'user'}
            </div>
        </div>
        <div class="wide_column_wrap">
            <div class="wide_column">
                {include "../components/page_block_header.xml", title => "invite"}
                <div class="invitebox page_block page_padding">
                    {_you_can_invite}<br />
                    <br />
                    <center>
                        <input type="text" readonly value="https://{$_SERVER["HTTP_HOST"]}/reg?ref={rawurlencode($thisUser->getRefLinkId())}" size="50" />
                    </center>
                    <p>{_you_can_invite_2}</p>
                </div>
            </div>
        </div>
    </div>

{/block}


