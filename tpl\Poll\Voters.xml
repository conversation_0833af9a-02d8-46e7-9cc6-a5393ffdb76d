{extends "../@listView.xml"}

{block title}
    {_poll_voters_list}
{/block}

{block header}
    {_poll_voters_list} »
    {$option[1]}
{/block}

{block tabs}
    <div n:foreach="$options as $optionId => $optionName" class="tab" id="{$optionId == $option[0] ? 'activetabs' : 'ki'}">
        <a id="{$optionId == $option[0] ? 'act_tab_a' : ''}" href="/poll{$pollId}/voters?option={base_convert($optionId, 10, 32)}">{$optionName}</a>
    </div>
{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block link|strip|stripHtml}
    {$x->getURL()}
{/block}

{block preview}
    <img src="{$x->getAvatarUrl('miniscule')}" width="75" alt="{_photo}" loading=lazy />
{/block}

{block name}
    {$x->getCanonicalName()}
    <img n:if="$x->isVerified()"
        class="name-checkmark"
        src="/assets/packages/static/openvk/img/checkmark.png"
        />
{/block}

{block description}
{/block}

{block actions}
{/block}