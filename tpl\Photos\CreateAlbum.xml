{extends "../@layout.xml"}
{block title}{_creating_album}{/block}

{block content}
{if isset($club)}
    {var $clubId = $club->getId()}
    {include "../components/page_crumb_header.xml", crumbs => [
        ['href' => $club->getURL(), 'title' => $club->getCanonicalName()],
        ['href' => "/albums-{$clubId}", 'title' => tr("albums")],
        ['title' => tr("creating_album")]
    ]}
{else}
    {include "../components/page_crumb_header.xml", crumbs => [
        ['href' => $thisUser->getURL(), 'title' => $thisUser->getCanonicalName()],
        ['href' => "/albums{$thisUser->getId()}", 'title' => tr("albums")],
        ['title' => tr("creating_album")]
    ]}
{/if}

<div class="page_block">
    <form method="post" enctype="multipart/form-data">
    <div class="settings_panel settings_padding group_settings" style="width: 455px;margin-inline: auto;">
        <div class="settings_list_row">
            <div class="settings_label">{_name}</div>
            <div class="settings_labeled_text">
                <input type="text" name="name" />
            </div>
        </div>
        <div class="settings_list_row">
            <div class="settings_label">{_description}</div>
            <div class="settings_labeled_text">
                <textarea name="desc"></textarea>
            </div>
        </div>
        <div class="settings_save_footer">
            <input type="hidden" name="hash" value="{$csrfToken}" />
            <input type="submit" class="button" name="submit" value="{_create}" />
        </div>
    </div>
    </form>
</div>
{/block}