{extends "../@layout.xml"}

{block title}
    {_apps}
{/block}

{block content}
    {var $tabs = [
        [
            'url' => '?act=list',
            'title' => 'all_apps',
            'active' => $act === 'list',
            'id' => 'apps_tab_list'
        ],
        [
            'url' => '?act=installed',
            'title' => 'installed_apps',
            'active' => $act === 'installed',
            'id' => 'apps_tab_installed'
        ],
        [
            'url' => '?act=dev',
            'title' => 'own_apps',
            'active' => $act === 'dev',
            'id' => 'apps_tab_dev'
        ]
    ]}
    {if $act === 'dev'}
        {include "../components/page_tabs_header.xml", tabs => $tabs, id => 'apps_top_tabs', extra => '<a class="button" href="/editapp?act=create">' . tr('create') . '</a>'}
    {else}
        {include "../components/page_tabs_header.xml", tabs => $tabs, id => 'apps_top_tabs'}
    {/if}
    <div class="page_block list_view">
        {var $data = is_array($iterator) ? $iterator : iterator_to_array($iterator)}

        {if sizeof($data) > 0}
            <div class="search_row" n:foreach="$data as $x">
                <div class="img">
                    <a href="/app{$x->getId()}">
                        <img style="max-width: 80px;" src="{$x->getAvatarUrl()}" />
                    </a>
                </div>
                <div class="info">
                    <div class="labeled name">
                        <a href="/app{$x->getId()}">
                            <b>
                                {$x->getName()}
                            </b>
                        </a>
                    </div>
                    <div class="labeled">
                        {$x->getDescription()}
                    </div>
                    <div class="labeled message">
                        <a n:if="$x->isInstalledBy($thisUser)" href="/apps/uninstall?hash={rawurlencode($csrfToken)}&app={$x->getId()}">{_app_uninstall|firstUpper}</a>{if $thisUser->getId() == $x->getOwner()->getId() && $x->isInstalledBy($thisUser)} • {/if}
                        <a n:if="$thisUser->getId() == $x->getOwner()->getId()" href="/editapp?app={$x->getId()}">{_app_edit|firstUpper}</a>
                    </div>
                </div>
                <div class="action_links controls">
                    <a href="/app{$x->getId()}" class="button button_wide">{_app_play|firstUpper}</a>
                </div>
            </div>
            {include "../components/paginator.xml", conf => (object) [
                "page"     => $page,
                "count"    => $count,
                "amount"   => sizeof($data),
                "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
                "atBottom" => true,
            ]}
        {else}
            {include "../components/nothing.xml"}
        {/if}
    </div>
{/block}
