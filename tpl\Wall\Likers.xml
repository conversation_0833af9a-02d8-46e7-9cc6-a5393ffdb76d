{extends "../@listView.xml"}

{block title}
    {_likers_list}
{/block}

{block header}
    <a href='{$item->getPageURL()}'>{tr($type)}</a> »
    {_likers_list}
{/block}

{block tabs}
    <div class="tab" id="activetabs">
        <a id="act_tab_a" href="#">{_liked_verb}</a>
    </div>
{/block}

{block link|strip|stripHtml}
    {$x->getURL()}
{/block}

{block preview}
    <img src="{$x->getAvatarUrl('tiny')}" width="75" loading=lazy />
{/block}

{block name}
    {$x->getCanonicalName()}
    <img n:if="$x->isVerified()"
        class="name-checkmark"
        src="/assets/packages/static/openvk/img/checkmark.png"
        />
{/block}

{block description}
    <table>
        <tbody>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_pronouns}: </span></td>
                <td>{$x->isFemale() ? tr("female") : ($x->isNeutral() ? tr("neutral") : tr("male"))}</td>
            </tr>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_relationship}:</span></td>
                <td>{$x->getLocalizedMaritalStatus()}</td>
            </tr>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_registration_date}: </span></td>
                <td>{$x->getRegistrationTime()}</td>
            </tr>
        </tbody>
    </table>
{/block}
