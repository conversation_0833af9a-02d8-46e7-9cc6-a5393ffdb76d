{extends "../@layout.xml"}
{var $topics = iterator_to_array($topics)}
{var $page     = $paginatorConf->page}

{block title}{_discussions} {$club->getCanonicalName()}{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs: [
        ['href' => $club->getURL(), 'title' => $club->getCanonicalName()],
        ['title' => tr("discussions"), 'count' => sizeof($topics)]
    ], extra => '<a class="button" href="/board'.$club->getId().'/create">'.tr("create_topic").'</a>'
    }
    {include "../components/search_bar.xml",
        action => '/board'.$club->getId(),
        input_value => $_GET['query'] ?? '',
        input_name => 'query',
        id => 'topics_search_header',
    }

    <div class="page_block list_view">
        {var $data = is_array($topics) ? $topics : iterator_to_array($topics)}

        {if sizeof($data) > 0}
            <div class="search_row" n:foreach="$data as $x">
                <div class="info fl_l">
                    <div class="labeled name">
                        <a href="/topic{$x->getPrettyId()}">
                            <b>
                                {$x->getTitle()}
                                <div n:if="$x->isPinned()" class="pinned-mark"></div>
                            </b>
                        </a>
                    </div>
                    <div class="labeled">
                        {tr("messages", $x->getCommentsCount())}
                    </div>
                    {var $lastComment = $x->getLastComment()}
                </div>
                <a href="/topic{$x->getPrettyId()}#_comment{$lastComment->getId()}" n:if="$lastComment" class="controls blist_last">
                    <div class="avatar">
                        <img class="ava" src="{$lastComment->getOwner()->getAvatarUrl()}" />
                    </div>
                    <div class="info">
                        <div class="labeled name">{$lastComment->getOwner()->getCanonicalName()}</div>
                        <div class="labeled">{_replied} {$lastComment->getPublicationTime()}</div>
                    </div>
                </a>
            </div>
            {include "../components/paginator.xml", conf => (object) [
                "page"     => $page,
                "count"    => $paginatorConf->count,
                "amount"   => sizeof($data),
                "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
                "atBottom" => true,
            ]}
        {else}
            {include "../components/nothing.xml"}
        {/if}
    </div>
{/block}
