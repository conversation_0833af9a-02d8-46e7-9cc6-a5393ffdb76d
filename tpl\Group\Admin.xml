{extends "../@layout.xml"}
{block title}{$manager->getUser()->getFullName()}{/block}

{block header}
    <a href="{$club->getURL()}">{$club->getName()}</a> » {_edit_group} » {$manager->getUser()->getFullName()}
{/block}

{block content}
    <div class="container_gray">
        <form method="POST" enctype="multipart/form-data" action="/club{$club->getId()}/setAdmin">
            <table cellspacing="7" cellpadding="0" width="40%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_comment}: </span>
                        </td>
                        <td>
                            <input type="text" name="comment" value="{$manager->getComment()}" />
                        </td>
                    </tr>
		            <tr>
                        <td width="120" valign="top">
                            <span class="nobold"></span>
                        </td>
                        <td>
                            <!-- TODO: <input type="checkbox" name="wall" value="1" {if $club->canPost()}checked{/if}/> Виден в контактах -->
                        </td>
                    </tr>

                    <tr>
                        <td>
                            
                        </td>
                        <td>
                            <input type="hidden" name="user" value="{$manager->getUser()->getId()}">
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
    </div>
{/block}
