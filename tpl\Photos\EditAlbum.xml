{extends "../@layout.xml"}
{block title}{_edit_album}{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs => [
        ['href' => $album->getOwner()->getURL(), 'title' => $album->getOwner()->getCanonicalName()],
        ['href' => ($album->getOwner() instanceof openvk\Web\Models\Entities\Club ? "/albums" . ($album->getOwner()->getId() * -1) : "/albums" . $album->getOwner()->getId()), 'title' => tr("albums")],
        ['href' => "/album{$album->getPrettyId()}", 'title' => $album->getName()],
        ['title' => tr("edit_album")]
    ]}
    <div class="page_block">
      <form method="post" enctype="multipart/form-data">
      <div class="settings_panel settings_padding edit_panel" style="width: 455px;margin-inline: auto;">
          <div class="settings_list_row">
              <div class="settings_label">{_name}</div>
              <div class="settings_labeled_text">
                  <input type="text" name="name" value="{$album->getName()}" />
              </div>
          </div>
          <div class="settings_list_row">
              <div class="settings_label">{_description}</div>
              <div class="settings_labeled_text">
                  <textarea name="desc">{$album->getDescription()}</textarea>
              </div>
          </div>
          <div class="settings_save_footer">
              <input type="hidden" name="hash" value="{$csrfToken}" />
              <input type="submit" class="button" name="submit" value="{_save}" />
          </div>
      </div>
      </form>
      <div class="settings_block_footer">
          {_you_can_also} <a href="/album{$album->getOwner() instanceof openvk\Web\Models\Entities\Club ? '-' : ''}{$album->getOwner()->getId()}_{$album->getId()}/delete?hash={rawurlencode($csrfToken)}">{_delete_album}</a>.
      </div>
    </div>
{/block}
