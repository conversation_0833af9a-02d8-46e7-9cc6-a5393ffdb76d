{extends "../@layout.xml"}
{var $backdrops = $club->getBackDropPictureURLs()}

{block title}{$club->getName()}{/block} 

{block content}
    <div class="wide_column_left">
        <div class="narrow_column_wrap">
            <div class="narrow_column">
			    {var $avatarPhoto = $club->getAvatarPhoto()}
    			{var $avatarLink = ((is_null($avatarPhoto) ? FALSE : $avatarPhoto->isAnonymous()) ? "/photo" . ("s/" . base_convert((string) $avatarPhoto->getId(), 10, 32)) : $club->getAvatarLink())}
				<div class="page_block photo_block">
					<div class="avatar_block" data-club="{$club->getId()}">
						{if $thisUser && $club->canBeModifiedBy($thisUser)}
							<a {if $avatarPhoto}style="display:none"{/if} class="add_image_text" id="add_image">{_add_image}</a>
							<div {if !$avatarPhoto}style="display:none"{/if} class="avatar_controls">
								<div class="avatarDelete hoverable"></div>
								<div class="avatar_variants">
									<a class="_add_image hoverable" id="add_image"><span>{_upload_new_picture}</span></a>
								</div>
							</div>
						{/if}
						<a href="{$avatarLink|nocheck}">
							<img src="{str_contains($club->getAvatarUrl('normal'), 'camera_200.png') ? '/themepack/vkify16/'.$theme->getVersion().'/resource/community_200.png' : $club->getAvatarUrl('normal')}"
								id="bigAvatar"
								style="width: 100%; image-rendering: -webkit-optimize-contrast;"
								{if ($club->getAvatarPhoto())}
								onclick="OpenMiniature(event, {$club->getAvatarUrl('normal')}, null, {$club->getAvatarPhoto()->getPrettyId()}, null)"
								{/if}
								/>
						</a>
					</div>
					<div class="profile_actions clear_fix">
						{var $subStatus = $club->getSubscriptionStatus($thisUser)}
						<div class="profile_actions_split">
							{var $actions = [
								0 => ['act' => 'add', 'label' => tr('join_community')],
								1 => ['act' => 'rem', 'label' => tr('leave_community'), 'class' => 'button_gray']
							]}
							{if isset($actions[$subStatus ? 1 : 0])}
								<form action="/setSub/club" method="post" class="profile_link_form fl_l">
									<input type="hidden" name="act" value="{$actions[$subStatus ? 1 : 0]['act']}" />
									<input type="hidden" name="id"  value="{$club->getId()}" />
									<input type="hidden" name="hash" value="{$csrfToken}" />
									<input type="submit" value="{$actions[$subStatus ? 1 : 0]['label']}" class="button button_wide {if isset($actions[$subStatus ? 1 : 0]['class'])}{$actions[$subStatus ? 1 : 0]['class']}{/if}" />
								</form>
							{/if}
							<button class="button button_gray profile_more_btn fl_r" id="profile_more_btn"><span class="clear_fix">&nbsp;</span></button>
							<div id="profile_actions_tooltip">
								<div class="tippy-menu">
									{if $club->canBeModifiedBy($thisUser)}
										<a href="/club{$club->getId()}/edit">{_edit_group}</a>
										<a href="/club{$club->getId()}/stats" rel="nofollow">{_statistics}</a>
										<div class="separator"></div>
									{/if}
									{if $thisUser->getChandlerUser()->can("access")->model("admin")->whichBelongsTo(NULL)}
										<a href="/admin/clubs/id{$club->getId()}">{_manage_group_action}</a>
										<a href="/admin/logs?obj_id={$club->getId()}&obj_type=Club">{_last_actions}</a>
										<div class="separator"></div>
									{/if}
									<a n:if="!$club->isHideFromGlobalFeedEnabled()" id="__ignoreSomeone" data-val='{!$ignore_status ? 1 : 0}' data-id="{$club->getRealId()}">
										{if !$ignore_status}{_ignore_club}{else}{_unignore_club}{/if}
									</a>
									{var $canReport = $thisUser->getId() != $club->getOwner()->getId()}
									<a n:if="$canReport" href="javascript:reportClub({$club->getId()})">{_report}</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				{var $followersCount = $club->getFollowersCount()}
				<div n:if="$thisUser && $followersCount > 0" class="page_block module people_module">

						<a href="/club{$club->getId()}/followers" class="module_header">
							<h3 class="header_top clear_fix">
								<span class="header_label fl_l">
									{_participants}
								</span>
								<span class="header_count fl_l">
									{$followersCount}
								</span>
							</h3>
						</a>
						<div class="module_body">
							<div class="search_row" n:foreach="array_chunk(array_slice(iterator_to_array($club->getFollowers(1)), 0, 6), 3) as $followerRow">
								<div class="people_cell" n:foreach="$followerRow as $follower">
									<a class="people_cell_ava" href="{$follower->getURL()}">
										<div class="people_cell_img">
											<img src="{$follower->getAvatarUrl('miniscule')}" width="50" height="50" alt="{$follower->getFirstName()}">
										</div>
									</a>
									<div class="people_cell_name"><a href="{$follower->getURL()}">{$follower->getFirstName()}</a></div>
								</div>
							</div>
						</div>
				</div>
				<div class="page_block">
					<div n:if="$club->isDisplayTopicsAboveWallEnabled()" class="module album_module">
						{if $albumsCount > 0}
							<a href="/albums{$club->getId() * -1}" class="module_header">
								<h3 class="header_top clear_fix">
									<span class="header_label fl_l">
										{_albums}
									</span>
									<span class="header_count fl_l">
										{$albumsCount}
									</span>
								</h3>
							</a>
							<div class="module_body clear_fix">
								<div class="clear_fix clear page_album_row" n:foreach="array_slice(iterator_to_array($albums), 0, 3) as $album">
									<a href="/album{$album->getPrettyId()}" class="page_album_link {if is_null($album->getCoverPhoto())}page_album_nocover{/if}">
										{var $cover = $album->getCoverPhoto()}
										<div class="page_album_thumb_wrap">
											<img n:if="!is_null($cover)"
												src="{$cover->getURLBySizeId('normal')}"
												class="page_album_thumb" loading="lazy" />
										</div>
										<div class="page_album_title">
											<div class="page_album_size">{$album->getPhotosCount()}</div>
											<div class="page_album_title_text">{$album->getName()}</div>
										</div>
									</a>
								</div>
							</div>
						{else}
							<a href="/albums{$club->getId() * -1}" class="page_module_upload">
								<div class="page_upload_label page_photos_upload"></div>{_upload_photo}</a>
						{/if}
					</div>

					<div n:if="($topicsCount > 0 || $club->isEveryoneCanCreateTopics()) && !$club->isDisplayTopicsAboveWallEnabled()" class="module topics_module">
						{if $topicsCount > 0}
							<a href="/board{$club->getId()}" class="module_header">
								<h3 class="header_top clear_fix">
									<span class="header_label fl_l">
										{_discussions}
									</span>
									<span class="header_count fl_l">
										{$topicsCount}
									</span>
								</h3>
							</a>
							<div class="module_body">
								<div class="topic_row" n:foreach="$topics as $topic">
									<div class="topic_info_wrap">
										<a href="/topic{$topic->getPrettyId()}">
											<div class="topic_icon"></div>
											<div class="topic_title">{$topic->getTitle()}</div>
										</a>
										<div class="topic_info">{tr("updated_at", $topic->getUpdateTime())}</div>
									</div>
								</div>
							</div>
						{else}
							<a href="/board{$club->getId()}" class="page_module_upload">
								<div class="page_upload_label page_topics_upload"></div>{_create_topic}</a>
						{/if}
					</div>

					<div n:if="($thisUser && $docsCount > 0 || ($thisUser && $club->canBeModifiedBy($thisUser)))" class="module">
						{if $docsCount > 0}
							<a href="/docs{$club->getRealId()}" class="module_header">
								<h3 class="header_top clear_fix">
									<span class="header_label fl_l">
										{_documents}
									</span>
									<span class="header_count fl_l">
										{$docsCount}
									</span>
								</h3>
							</a>
							<div class="module_body">
								{foreach $docs as $doc}
									{include "../Documents/components/doc.xml", doc => $doc, hideButtons => true, noTags => true}
								{/foreach}
							</div>
						{else}
							<a href="/docs{$club->getRealId()}" class="page_module_upload">
								<div class="page_upload_label page_docs_upload"></div>{_document_uploading_in_general}</a>
						{/if}
					</div>

					<div n:if="$thisUser" class="module">
						{if $audiosCount > 0}
							<a href="/audios-{$club->getId()}" class="module_header">
								<h3 class="header_top clear_fix">
									<span class="header_label fl_l">
										{_audios}
									</span>
									<span class="header_count fl_l">
										{$audiosCount}
									</span>
								</h3>
							</a>
							<div class="module_body">
								<div class="content_list long">
									<div class="audio" n:foreach="$audios as $audio" style="width: 100%;">
										{include "../Audio/player.xml", audio => $audio}
									</div>
								</div>
							</div>
						{else}
							<a href="/audios-{$club->getId()}" class="page_module_upload">
								<div class="page_upload_label page_audios_upload"></div>{_add_audio_to_club}</a>
						{/if}
					</div>
				</div>

				<div n:ifcontent class="page_block">
					<div n:if="$club->getAdministratorsListDisplay() == 0" class="module page_list_module">
						<a href="/club{$club->getId()}/followers?onlyAdmins=1" class="module_header">
							<h3 class="header_top clear_fix">
								<span class="header_label fl_l">
									{_creator}
								</span>
							</h3>
						</a>

						<div class="module_body">
							<div class="line_cell clear_fix">
								{var $author = $club->getOwner()}
								<div class="thumb">
									<a href="{$author->getURL()}">
										<img class="cell_img" src="{$author->getAvatarUrl()}" />
									</a>
								</div>
								<div n:class="empty($club->getOwnerComment()) ? 'info' : 'desc_info'">
									<a href="{$author->getURL()}" class="people_name">{$author->getCanonicalName()}</a>
									<div n:ifcontent class="people_desc">{$club->getOwnerComment()}</div>
								</div>
							</div>
						</div>
					</div>
					<div n:if="$club->getAdministratorsListDisplay() == 1" class="module page_list_module">
						{var $managersCount = $club->getManagersCount(true)}
						<a href="/club{$club->getId()}/followers?onlyAdmins=1" class="module_header">
							<h3 class="header_top clear_fix">
								<span class="header_label fl_l">
									{_administrators}
								</span>
								<span class="header_count fl_l">
									{$managersCount}
								</span>
							</h3>
						</a>

						<div class="module_body">
							<div class="line_cell clear_fix" n:if="!$club->isOwnerHidden()">
								{var $author = $club->getOwner()}
								<div class="thumb">
									<a href="{$author->getURL()}">
										<img class="cell_img" src="{$author->getAvatarUrl()}" />
									</a>
								</div>
								<div n:class="empty($club->getOwnerComment()) ? 'info' : 'desc_info'">
									<a href="{$author->getURL()}" class="people_name">{$author->getCanonicalName()}</a>
									<div n:ifcontent class="people_desc">{$club->getOwnerComment()}</div>
								</div>
							</div>
							<div class="line_cell clear_fix" n:foreach="$club->getManagers(1, true) as $manager">
								{var $user = $manager->getUser()}
								<div class="thumb">
									<a href="{$user->getURL()}">
										<img class="cell_img" src="{$user->getAvatarUrl()}" />
									</a>
								</div>
								<div n:class="empty($manager->getComment()) ? 'info' : 'desc_info'">
									<a href="{$user->getURL()}" class="people_name">{$user->getCanonicalName()}</a>
									<div n:ifcontent class="people_desc">{$manager->getComment()}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="wide_column_wrap">
			<div class="wide_column">
				<div n:if="!is_null($alert = $club->getAlert())" class="msg msg_yellow">{strpos($alert, "@") === 0 ? tr(substr($alert, 1)) : $alert}</div>
				<div class="page_block">
					<div class="page_top">
						<h2 class="page_name">{$club->getName()}
							<a class="page_verified" n:if="$club->isVerified()" href="/verify"></a>
						</h2>
					</div>
					<div class="page_block_h2 page_info_header_tabs">
						<ul class="ui_tabs clear_fix page_info_tabs">
							<li class="ui_tab_default"><div class="ui_tab_plain" href="/club{$club->getId()}">{_information}</div></li>
						</ul>
					</div>	
					<div class="page_info_wrap info">
						<div class="group_info_block info">
							<div class="group_info_rows">
								<div n:if="$club->getDescriptionHtml()" class="group_info_row info">
									<div class="line_value">{$club->getDescriptionHtml()|noescape}</div>
								</div>
								<div n:if="$club->getWebsite()" class="group_info_row site">
									<div class="line_value"><a n:if="$club->getWebsite()" href="{$club->getWebsite()}" rel="ugc" target="_blank">{$club->getWebsite()}</a></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div n:if="($topicsCount > 0 || $club->isEveryoneCanCreateTopics() || ($thisUser && $club->canBeModifiedBy($thisUser))) && $club->isDisplayTopicsAboveWallEnabled()"
				class="page_block module topics_module">
					{if $topicsCount > 0}
						<a href="/board{$club->getId()}" class="module_header">
							<div class="header_top clear_fix">
								<span class="header_label fl_l">
									{_discussions}
								</span>
								<span class="header_count fl_l">
									{$topicsCount}
								</span>
							</div>
						</a>
						<div class="module_body">
							<div class="topic_row" n:foreach="$topics as $topic">
								<div class="topic_info_wrap">
									<a href="/topic{$topic->getPrettyId()}">
										<div class="topic_icon"></div>
										<div class="topic_title">{$topic->getTitle()}</div>
									</a>
									<div class="topic_info">{tr("updated_at", $topic->getUpdateTime())}</div>
								</div>
							</div>
						</div>
					{else}
						<a href="/board{$club->getId()}" class="page_module_upload">
							<div class="page_upload_label page_topics_upload"></div>Start a discussion</a>
					{/if}
				</div>
				
				<div n:if="!is_null($suggestedPostsCountByUser) && $suggestedPostsCountByUser > 0" class="sugglist hidden">
					{$suggestedPostsCountByUser}
				</div>

				<div n:if="!is_null($suggestedPostsCountByEveryone) && $suggestedPostsCountByEveryone > 0" class="sugglist hidden">
					{$suggestedPostsCountByEveryone}
				</div>

				{presenter "openvk!Wall->wallEmbedded", -$club->getId()}
			</div>
		</div>
	</div>
	{* детка, ты сделала костыль на 5 с плюсом ! *}
	{* ты меня зажгла.. появилось оч сильное и реальное желание сделать ещё парочку.. нет.. именно намутить хорошенько. *}
	{* чтобы папа Вова и дальше был доволен, жду в ближайшее время всех описанных и перечисленных тобою костылей. ну кроме последнего сценария..)) хотя... ;) *}
	{* кстати насчет костыля в виде диалогов... в js отлично, а я еще люблю когда они как бы случайно совсем чуть-чуть отваливаются... *}
	{* и раз уж ты такая плохая девочка, вот видео как раз для тебя: https://ovk.to/video8120_17 *}
    <script n:if="isset($thisUser) && $club->getWallType() == 2 && !$club->canBeModifiedBy($thisUser)">
        document.querySelector("textarea").setAttribute("placeholder", tr("suggest_new"))
    </script>
</div>

{/block}
