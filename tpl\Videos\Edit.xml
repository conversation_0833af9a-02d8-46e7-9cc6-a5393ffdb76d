{extends "../@layout.xml"}
{block title}{_change_video}{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs: [
        ['href' => $thisUser->getURL(), 'title' => $thisUser->getCanonicalName()],
        ['href' => "/videos{$thisUser->getId()}", 'title' => tr("videos")],
        ['href' => "/video{$video->getPrettyId()}", 'title' => $video->getName()],
        ['title' => tr("change_video")]
    ]}
<div class="page_block">
    <form method="post" enctype="multipart/form-data">
      <div class="settings_panel settings_padding" style="width: 455px;margin-inline: auto;">
        <div class="settings_list_row">
            <div class="settings_label">{_name}</div>
            <div class="settings_labeled_text">
                <input type="text" name="name" value="{$video->getName()}" />
            </div>
        </div>
        <div class="settings_list_row">
            <div class="settings_label">{_description}</div>
            <div class="settings_labeled_text">
                <textarea name="desc">{$video->getDescription()}</textarea>
            </div>
        </div>
        <div class="settings_save_footer">
            <input type="hidden" name="hash" value="{$csrfToken}" />
            <input type="submit" class="button" name="submit" value="{_save}" />
        </div>
      </div>
    </form>
</div>
{/block}