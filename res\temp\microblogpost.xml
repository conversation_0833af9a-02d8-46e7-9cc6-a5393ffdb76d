{var $author = $post->getOwner()}
{var $comments = $post->getLastComments(3)}
{var $commentsCount = $post->getCommentsCount()}
{var $platform = $post->getPlatform()}
{var $platformDetails = $post->getPlatformDetails()}
{var $likesCount = $post->getLikesCount()}
{var $repostsCount = $post->getRepostCount()}
{var $canBePinned = $post->canBePinnedBy($thisUser ?? NULL)}
{var $canBeDeleted = $post->canBeDeletedBy($thisUser)}
{var $wallOwner = $post->getWallOwner()}
{if $post->isDeactivationMessage() && $post->getText()}
    {var $deac = "post_deact"}
{else}
    {var $deac = "post_deact_silent"}
{/if}
{var $compact = isset($compact) ? true : false}
{var $club = isset($club) ? $club}

{var $commentTextAreaId = $post === NULL ? rand(1,300) : $post->getId()}

<table border="0" style="font-size: 11px;" data-id="{$post->getPrettyId()}" n:class="post, !$compact ? post-divider, $post->isExplicit() ? post-nsfw">
    <tbody>
        <tr>
            <td width="54" valign="top">
                <a href="{$author->getURL()}">
                    <img src="{$author->getAvatarURL('miniscule')}" width="{if $compact}25{else}50{/if}" class="post-avatar {if $compact}cCompactAvatars{/if}" />
                    <span n:if="!$post->isPostedOnBehalfOfGroup() && !$compact && $author->isOnline()" class="post-online">{_online}</span>
                </a>
            </td>
            <td width="100%" valign="top">
                <div class="post-author">
                    <a href="{$author->getURL()}"><b class="post-author-name">{$author->getCanonicalName()}</b></a>
                    <img n:if="$author->isVerified()" class="name-checkmark" src="/assets/packages/static/openvk/img/checkmark.png">
                    {$post->isDeactivationMessage() ? ($author->isFemale() ? tr($deac . "_f") : ($author->isNeutral() ? tr($deac . "_g") : tr($deac . "_m")))}
                    {$post->isUpdateAvatarMessage() && !$post->isPostedOnBehalfOfGroup() ? ($author->isFemale() ? tr("upd_f") : ($author->isNeutral() ? tr("upd_n") : tr("upd_m")))}
                    {$post->isUpdateAvatarMessage() && $post->isPostedOnBehalfOfGroup() ? tr("upd_g") : ""}
                    {if ($onWallOf ?? false) &&!$post->isPostedOnBehalfOfGroup() && $post->getOwnerPost() !== $post->getTargetWall()}
                        <a href="{$wallOwner->getURL()}" class="mention" data-mention-ref="{$post->getTargetWall()}">
                            <b>
                                {if isset($thisUser) && $thisUser->getId() === $post->getTargetWall()}
                                {_post_on_your_wall}
                                {elseif $wallOwner instanceof \openvk\Web\Models\Entities\Club}
                                {tr("post_on_group_wall", ovk_proc_strtr($wallOwner->getName(), 52))}
                                {else}
                                {tr("post_on_user_wall", $wallOwner->getMorphedName("genitive", false))}
                                {/if}
                            </b>
                        </a>
                    {/if}

                    {if $compact}
                        <br>
                        <a href="/wall{$post->getPrettyId()}" class="date">
                            {$post->getPublicationTime()}
                        </a>
                    {/if}

                    <span n:if="$post->isPinned()" class="nobold">{_pinned}</span>

                    <a n:if="$canBeDeleted && !($forceNoDeleteLink ?? false) && $compact == false" class="delete" href="/wall{$post->getPrettyId()}/delete"></a>
                    <a n:if="!$canBeDeleted" class="report" title="{_report}" href="javascript:reportPost({$post->getId()})"></a>
                    
                    {if $canBePinned && !($forceNoPinLink ?? false) && $compact == false}
                        {if $post->isPinned()}
                            <a class="pin" href="/wall{$post->getPrettyId()}/pin?act=unpin&hash={rawurlencode($csrfToken)}"></a>
                        {else}
                            <a class="pin" href="/wall{$post->getPrettyId()}/pin?act=pin&hash={rawurlencode($csrfToken)}"></a>
                        {/if}
                    {/if}

                    {if $post->canBeEditedBy($thisUser) && !($forceNoEditLink ?? false) && $compact == false}
                        <a class="edit" id="editPost"></a>
                    {/if}
                </div>
                <div class="post-content" id="{$post->getPrettyId()}" data-localized-nsfw-text="{_nsfw_warning}">
                    <div class="text" id="text{$post->getPrettyId()}">
                        <span data-text="{$post->getText(false)}" class="really_text">{$post->getText()|noescape}</span>
                        
                        {var $width = ($GLOBALS["_bigWall"] ?? false) ? 550 : 320}
                        {if isset($GLOBALS["_nesAttGloCou"])}
                            {var $width = $width - 70 * $GLOBALS["_nesAttGloCou"]}
                        {/if}
                        {var $attachmentsLayout = $post->getChildrenWithLayout($width)}
                        <div n:ifcontent class="attachments attachments_b" style="height: {$attachmentsLayout->height|noescape}; width: {$attachmentsLayout->width|noescape};">
                            <div class="attachment" n:foreach="$attachmentsLayout->tiles as $attachment" style="float: {$attachment[3]|noescape}; width: {$attachment[0]|noescape}; height: {$attachment[1]|noescape};">
                                {include "../attachment.xml", attachment => $attachment[2], parent => $post, parentType => "post", tilesCount => sizeof($attachmentsLayout->tiles)}
                            </div>
                        </div>

                        <div n:ifcontent class="attachments attachments_m">
                            <div class="attachment" n:foreach="$attachmentsLayout->extras as $attachment">
                                {include "../attachment.xml", attachment => $attachment, post => $post}
                            </div>
                        </div>
                    </div>
                    <div n:if="$post->getGeo()" class="post-geo">
                        <a onclick="javascript:openGeo({$post->getGeo()}, {$post->getTargetWall()}, {$post->getVirtualId()})">
                            <svg class="map_svg_icon" width="13" height="12" viewBox="0 0 3.4395833 3.175">
                                <g><path d="M 1.7197917 0.0025838216 C 1.1850116 0.0049444593 0.72280427 0.4971031 0.71520182 1.0190592 C 0.70756921 1.5430869 1.7223755 3.1739665 1.7223755 3.1739665 C 1.7223755 3.1739665 2.7249195 1.5439189 2.7243815 0.99632161 C 2.7238745 0.48024825 2.2492929 0.00024648357 1.7197917 0.0025838216 z M 1.7197917 0.52606608 A 0.48526123 0.48526123 0 0 1 2.2050334 1.0113078 A 0.48526123 0.48526123 0 0 1 1.7197917 1.4965495 A 0.48526123 0.48526123 0 0 1 1.23455 1.0113078 A 0.48526123 0.48526123 0 0 1 1.7197917 0.52606608 z " /></g>
                            </svg>
                            {$post->getGeo()->name ?? tr("admin_open")}
                        </a>
                    </div>
                    <div n:if="$post->isAd()" style="color:grey;">
                        <br/>
                        &nbsp;! {_post_is_ad}
                    </div>
                    <div n:if="$post->hasSource()" class="sourceDiv">
                        <span>{_source}: {$post->getSource(true)|noescape}</span>
                    </div>
                    <div n:if="$post->isSigned()" class="post-signature">
                        {var $actualAuthor = $post->getOwner(false)}
                        <span>
                            <div class="authorIcon"></div>
                            <a href="{$actualAuthor->getURL()}" class="mention authorName" data-mention-ref="{$actualAuthor->getId()}">
                               {$actualAuthor->getCanonicalName()}
                            </a>
                        </span>
                    </div>
                </div>
                <div class='post-edit' n:if='!$compact'></div>
                <div class="post-menu" n:if="$compact == false">
                    <a href="{if !$suggestion}/wall{$post->getPrettyId()}{else}javascript:void(0){/if}" class="date">{$post->getPublicationTime()}
                        <span n:if="$post->getEditTime()" class="edited editedMark">({_edited_short})</span>
                    </a>
                  
                    <a n:if="!empty($platform)" class="client_app" data-app-tag="{$platform}" data-app-name="{$platformDetails['name']}" data-app-url="{$platformDetails['url']}" data-app-img="{$platformDetails['img']}">
                        <img src="/assets/packages/static/openvk/img/app_icons_mini/{$post->getPlatform(this)}.svg">
                    </a>
                    {if isset($thisUser)}
                        &nbsp;

                        <a n:if="!($forceNoCommentsLink ?? false) && $commentsCount == 0" href="javascript:expand_comment_textarea({$commentTextAreaId})">{_comment}</a>
                        
                        <div class="like_wrap">
                            <a n:if="!($forceNoShareLink ?? false)" id="reposts{$post->getPrettyId()}" class="post-share-button" href="javascript:repost('{$post->getPrettyId()}', 'post')">
                                <div class="repost-icon" style="opacity: 0.4;"></div>
                                <span class="likeCnt" id="repostsCount{$post->getPrettyId()}">{if $repostsCount > 0}{$repostsCount}{/if}</span>
                            </a>

                            {if !($forceNoLike ?? false)}
                                {var $liked = $post->hasLikeFrom($thisUser)}
                                <a href="/wall{$post->getPrettyId()}/like?hash={rawurlencode($csrfToken)}" class="post-like-button" data-liked="{(int) $liked}" data-likes="{$likesCount}" data-id="{$post->getPrettyId()}" data-type='post'>
                                    <div class="heart" id="{if $liked}liked{/if}"></div>
                                    <span class="likeCnt">{if $likesCount > 0}{$likesCount}{/if}</span>
                                </a>
                            {/if}
                        </div>
                    {/if}
                </div>
                <div n:if="!($forceNoCommentsLink ?? false) && $commentSection == true && $compact == false" class="post-menu-s">
                    <a n:if="$commentsCount > 3" href="/wall{$post->getPrettyId()}" class="expand_button">{_view_other_comments} ({$commentsCount - 3})</a>
                    {foreach $comments as $comment}
                        {include "../comment.xml", comment => $comment, $compact => true}
                    {/foreach}
                    <div n:ifset="$thisUser" id="commentTextArea{$commentTextAreaId}" n:attr="style => ($commentsCount == 0 ? 'display: none;')" class="commentsTextFieldWrap">
                        {var $commentsURL = "/al_comments/create/posts/" . $post->getId()}
                        {var $club = is_null($club) ? ($post->getTargetWall() < 0 ? (new openvk\Web\Models\Repositories\Clubs)->get(abs($post->getTargetWall())) : NULL) : $club}
                        {include "../textArea.xml", route => $commentsURL, postOpts => false, graffiti => (bool) ovkGetQuirk("comments.allow-graffiti"), post => $post, club => $club}
                    </div>
                </div>
                <div n:if="$suggestion && $canBePinned" class="suggestionControls">
                    <input type="button" class="button" id="publish_post" data-id="{$post->getId()}" value="{_publish_suggested}">
                    <input type="button" class="button" id="decline_post" data-id="{$post->getId()}" value="{_decline_suggested}">
                </div>
            </td>
        </tr>
    </tbody>
</table>
