{extends "../@listView.xml"}
{var $perPage = 6} {* Why 6? Check User::_abstractRelationGenerator *}

{var $act = $_GET["act"] ?? "friends"}

{if $act == "incoming"}
    {var $iterator = iterator_to_array($user->getRequests($page))}
    {var $count    = $user->getRequestsCount()}
{elseif $act == "outcoming"}
    {var $iterator = iterator_to_array($user->getSubscriptions($page))}
    {var $count    = $user->getSubscriptionsCount()}
{elseif $act == "followers"}
    {var $iterator = iterator_to_array($user->getFollowers($page))}
    {var $count    = $user->getFollowersCount()}
{elseif $act == "online"}
    {var $iterator = iterator_to_array($user->getFriendsOnline($page))}
    {var $count    = $user->getFriendsOnlineCount()}
{else}
    {var $iterator = iterator_to_array($user->getFriends($page))}
    {var $count    = $user->getFriendsCount()}
{/if}

{block title}
    {if $act == "incoming"}
        {_incoming_req}
    {elseif $act == "outcoming"}
        {_outcoming_req}
    {elseif $act == "followers"}
        {_followers}
    {elseif $act == "online"}
        {_friends_online}
    {else}
        {_friends}
    {/if}
{/block} 

{block rtabs}
    {var $menuItems = [
        [
            'url' => '/friends' . $thisUser->getId(),
            'title' => 'all_friends',
            'active' => $act === 'friends',
            'extraItem' => [
                'content' => $act === 'friends' ? $count : ''
            ]
        ],
        [
            'url' => '?act=online',
            'title' => 'online',
            'active' => $act === 'online',
            'extraItem' => [
                'content' => $act === 'online' ? $count : ''
            ]
        ],
        [
            'url' => '?act=incoming',
            'title' => 'req',
            'active' => $act === 'incoming' || $act === 'followers' || $act === 'outcoming',
            'condition' => !is_null($thisUser) && $user->getId() === $thisUser->getId(),
            'extraItem' => [
                'content' => ($act === 'incoming' || $act === 'followers' || $act === 'outcoming') ? $count : ''
            ]
        ],
        [
            'isSeparator' => true
        ],
        [
            'url' => '/invite',
            'title' => 'invite',
            'active' => false,
            'condition' => isset($thisUser) && $thisUser->getId() == $user->getId()
        ]
    ]}
    {include "../components/ui_rmenu.xml", items => $menuItems}
{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block size}
    {var $title = !is_null($thisUser) && $user->getId() === $thisUser->getId()
        ? ($act == "incoming" || $act == "outcoming" ? "req"
            : ($act == "followers" ? "followers"
                : ($act == "online" ? "friends_online" : "all_friends")))
        : "friends"}
    {include "../components/page_block_header.xml", title => $title, count => $count}
{/block}

{block link|strip|stripHtml}
    {$x->getURL()}
{/block}

{block preview}
    <img src="{if str_contains($x->getAvatarUrl('miniscule'), 'camera_200.png')}
        /themepack/vkify16/{$theme->getVersion()}/resource/camera_200.png
    {else}
        {$x->getAvatarUrl('miniscule')}
        {/if}" width="75" alt="{_photo}" loading=lazy />
{/block}

{block name}
    {$x->getCanonicalName()}
    <a href="/verify" n:if="$x->isVerified()" 
        class="page_verified" >
    </a>
{/block}

{block description}
    <div class="labeled">
        {$x->isFemale() ? tr("female") : ($x->isNeutral() ? tr("neutral") : tr("male"))} {if !is_null($x->getCity())}<span class="divider"></span> {$x->getCity()}{/if}
    </div>
    {if $x->getSubscriptionStatus($thisUser) === 3}
        <div class="labeled message">
            <a href="/im?sel={$x->getId()}" rel="nofollow">{_send_message}</a>
        </div>
    {/if}
{/block}

{block actions}
    {if ($x->getId() !== $thisUser->getId()) && ($thisUser->getId() === $user->getId())}
        {var $subStatus = $x->getSubscriptionStatus($thisUser)}
        {if $subStatus === 0}
            <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                <input type="hidden" name="act" value="add" />
                <input type="hidden" name="id"  value="{$x->getId()}" />
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="button button_wide button_small" value="{_friends_add}" />
            </form>
        {elseif $subStatus === 1}
            <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                <input type="hidden" name="act" value="add" />
                <input type="hidden" name="id"  value="{$x->getId()}" />
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="button button_wide button_small" value="{_friends_accept}" />
            </form>
            {if $act !== 'followers'}
                <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                    <input type="hidden" name="act" value="rej" />
                    <input type="hidden" name="id"  value="{$x->getId()}" />
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                    <input type="submit" class="button button_wide button_gray button_small" value="{_friends_leave_in_flw}" />
                </form>
            {/if}
        {elseif $subStatus === 2}
            <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                <input type="hidden" name="act" value="rem" />
                <input type="hidden" name="id"  value="{$x->getId()}" />
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="button button_wide button_gray button_gray" value="{_friends_reject}" />
            </form>
        {elseif $subStatus === 3}
            <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                <input type="hidden" name="act" value="rem" />
                <input type="hidden" name="id"  value="{$x->getId()}" />
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="button button_wide button_gray button_gray" value="{_friends_delete}" />
            </form>
        {/if}
    {/if}
{/block}