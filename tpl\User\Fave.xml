{extends "../@layout.xml"}

{block title}{_bookmarks_tab}{/block}

{block content}
    {var $isPosts    = $section === 'posts'}
    {var $isComments = $section === 'comments'}
    {var $isPhotos   = $section === 'photos'}
    {var $isVideos   = $section === 'videos'}

    {var $menuItems = [
        [
            'url' => '/fave?section=posts',
            'title' => 's_posts',
            'active' => $isPosts
        ],
        [
            'url' => '/fave?section=comments',
            'title' => 's_comments',
            'active' => $isComments
        ],
        [
            'url' => '/fave?section=photos',
            'title' => 's_photos',
            'active' => $isPhotos
        ],
        [
            'url' => '/fave?section=videos',
            'title' => 's_videos',
            'active' => $isVideos
        ]
    ]}

    <div class="wide_column_left">
        <div class="narrow_column_wrap">
            <div class="narrow_column">
                {include "../components/ui_rmenu.xml", items => $menuItems}
            </div>
        </div>
        <div class="wide_column_wrap">
            <div class="wide_column">
                    {include "../components/page_block_header.xml", title => "bookmarks_tab"}
                    <div n:class='scroll_container, ($section == "photos" && $count > 0) ? album-flex'>
                        {if $count > 0}
                            {foreach $data as $dat}
                                {if $dat->isDeleted()}
                                    <div n:class="page_block, page_padding, deleted_mark, $section == 'photos' ? album-photo : deleted_mark_average">
                                        <span>[deleted]</span>
                                    </div>
                                {else}
                                    {if $section == "posts"}
                                        {include "../components/post.xml", post => $dat, commentSection => true}
                                    {elseif $section == "comments"}
                                        <div class="page_block">
                                            <div class="scroll_node">
                                                {include "../components/comment.xml", comment => $dat, correctLink => true, no_reply_button => true}
                                            </div>
                                        </div>
                                    {elseif $section == "photos"}
                                        <div class="page_block">
                                            <div class="album-photo scroll_node" onclick="OpenMiniature(event, {$dat->getURLBySizeId('larger')}, null, {$dat->getPrettyId()}, null)">
                                                <a href="/photo{$dat->getPrettyId()}">
                                                    <img class="album-photo--image" src="{$dat->getURLBySizeId('tinier')}" alt="{$dat->getDescription()}"  loading="lazy" />
                                                </a>
                                            </div>
                                        </div>
                                    {elseif $section == "videos"}
                                        <div class="page_block">
                                            <div class="scroll_node">
                                                {include "../components/video.xml", video => $dat}
                                            </div>
                                        </div>
                                    {/if}
                                {/if}
                            {/foreach}
                        {else}
                            {include "../components/content_error.xml", description => tr("faves_".$section."_empty_tip")}
                        {/if}
                    </div>

                    <div n:if='$paginatorConf->pageCount > 1' class='page_content_paginator_bottom'>
                        {include "../components/paginator.xml", conf => $extendedPaginatorConf}
                    </div>
            </div>
        </div>
    </div>
{/block}
