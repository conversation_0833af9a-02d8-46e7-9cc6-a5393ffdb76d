{extends "@layout.xml"}
{block title}{_banned_title}{/block}

{block header}
    {_banned_header}
{/block}

{block content}
    <div class="page_block module_body">
        <center>
            <img src="/assets/packages/static/openvk/img/oof.apng" alt="{_banned_alt}" style="width: 20%;" />
        </center>
        <p>
            {var $ban = $thisUser->getBanReason("banned")}
            {if is_string($ban)}
                {tr("banned_1", htmlentities($thisUser->getCanonicalName()))|noescape}<br/>
                {tr("banned_2", htmlentities($thisUser->getBanReason()))|noescape}
            {else}
                {tr("banned_1", htmlentities($thisUser->getCanonicalName()))|noescape}
                <div>
                    Эта страница была заморожена {$ban[0]|noescape}
                    {if $ban[1] !== "app"}
                        {include "Report/ViewContent.xml", type => $ban[1], object => $ban[2]}
                    {/if}
                </div>
            {/if}

            {if !$thisUser->getUnbanTime()}
                {_banned_perm}
            {else}
                {tr("banned_until_time", $thisUser->getUnbanTime())|noescape}
            {/if}
        </p>
        <p n:if="$thisUser->canUnbanThemself()">
            <hr/>
            <center><a class="button" href="/unban.php">{_banned_unban_myself}</a></center>
        </p>
        <hr/>
        <p>
            {tr("banned_3", urlencode($csrfToken))|noescape}
        </p>
    </div>
{/block}
