{block content}
    <div class="video_item scroll_node">
        <a href="/video{$video->getPrettyId()}">
            <div class="video_item_thumb_wrap" data-id="{$video->getPrettyId()}">
                <img id="videoOpen" src="{$video->getThumbnailURL()}"
                    alt="{$video->getName()}"
                    class="video_item_thumb "/>
                <div id="videoOpen" data-id="{$video->getPrettyId()}" class="video_item_controls">
                    <div class="video_thumb_label">
                    {if $video->getType() != 0}
                        <span class="video_thumb_label_item">
                            <vkifyloc name="external" />
                        </span>
                        <div class="divider" n:if="$video->getFormattedLength() != ''"></div>
                    {/if}
                        <span class="video_thumb_label_item">{$video->getFormattedLength()}</span>
                    </div>
                    <div class="video_thumb_play">
                    </div>
                </div>
                <div class="video_thumb_actions" n:if="$thisUser->getId() === $video->getOwner()->getId()">
                    <div>
                        <a href="/video{$video->getPrettyId()}/edit" class="video_thumb_action_edit" title="{tr('edit')}">
                            <div class="icon"></div>
                        </a>
                    </div>
                    <div>
                        <a href="/video{$video->getPrettyId()}/remove" class="video_thumb_action_delete" title="{tr('delete')}">
                            <div class="icon"></div>
                        </a>
                    </div>
                </div>
                <div class="video_thumb_actions" n:if="$thisUser->getId() != $video->getOwner()->getId()">
                    <div>
                        <a href="javascript:reportVideo({$video->getId()})" class="video_thumb_action_delete" title="{tr('report')}">
                            <div class="icon"></div>
                        </a>
                    </div>
                </div>
            </div>
        </a>
        <div class="video_item_info">
            <a class="video_item_title" href="/video{$video->getPrettyId()}">
                <span id="videoOpen" data-id="{$video->getPrettyId()}">{$video->getName()}</span>
            </a>
            <a class="video_item_author" href="/{$video->getOwner()->getId()}">{$video->getOwner()->getCanonicalName()}</a>
            <div class="video_item_add_info">
                <span class="video_item_date_info">{$video->getPublicationTime()}</span>
            </div>
        </div>
    </div>
{/block}