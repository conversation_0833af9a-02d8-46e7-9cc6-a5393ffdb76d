{extends "../@layout.xml"}
{block title}{_upload_photo}{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs => [
        ['href' => $album->getOwner()->getURL(), 'title' => $album->getOwner()->getCanonicalName()],
        ['href' => ($album->getOwner() instanceof openvk\Web\Models\Entities\Club ? "/albums" . ($album->getOwner()->getId() * -1) : "/albums" . $album->getOwner()->getId()), 'title' => tr("albums")],
        ['href' => "/album{$album->getPrettyId()}", 'title' => $album->getName(), 'count' => $album->getPhotosCount()],
        ['title' => tr("upload_photo")]
    ]}

    <input type="file" accept=".jpg,.png,.gif" name="files[]" multiple class="button photo_ajax_upload_button" id="uploadButton" style="display:none">

    <div class="page_block page_padding photo_upload_container">
        <div class="insertThere"></div>
        <div class="whiteBox">
            <h4>{_uploading_photos_from_computer}</h4>

            <div class="limits" style="margin-top:17px">
                <b style="color:#45688E">{_admin_limits}</b>
                <ul style="margin-top: 6px;">
                    <li>{_supported_formats}</li>
                    <li>{_max_load_photos}</li>
                </ul>

                <div style="text-align: center;padding-top: 4px;" class="insertAgain">
                    <input type="button" class="button" id="fakeButton" onclick="uploadButton.click()" value="{_upload_picts}">
                </div>

                <div class="tipping" style="margin-top: 19px;">
                    <span style="line-height: 15px"><b>{_tip}</b>: {_tip_ctrl}</span>
                </div>
            </div>
            <div class="insertPhotos" id="photos" style="margin-top: 9px;padding-bottom: 12px;"></div>

            <input type="button" class="button" style="display:none;margin-left: auto;margin-right: auto;" id="endUploading" value="{_end_uploading}">
        </div>
    </div>

    <input n:ifset="$_GET['album']" type="hidden" id="album" value="{$_GET['album']}" />

    <script>
        uploadButton.value = ''
    </script>
{/block}
