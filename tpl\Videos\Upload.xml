{extends "../@layout.xml"}
{block title}{_upload_video}{/block}

{block content}
<div class="page_block">
    {include "../components/page_crumb_header.xml", crumbs: [
        ['href' => $thisUser->getURL(), 'title' => $thisUser->getCanonicalName()],
        ['href' => "/videos{$thisUser->getId()}", 'title' => tr("videos")],
        ['title' => tr("upload_video")]
    ]}
    <form method="post" enctype="multipart/form-data">
      <div class="settings_panel settings_padding" style="width: 455px;margin-inline: auto;">
        <div class="settings_list_row">
            <div class="settings_label">{_name}</div>
            <div class="settings_labeled_text">
                <input type="text" name="name" />
            </div>
        </div>
        <div class="settings_list_row">
            <div class="settings_label">{_description}</div>
            <div class="settings_labeled_text">
                <textarea name="desc"></textarea>
            </div>
        </div>
        <div class="settings_list_row">
            <div class="settings_label">{_video}</div>
            <div class="settings_labeled_text">
                <label class="button" style="">{_browse}
                    <input type="file" id="blob" name="blob" style="display: none;" onchange="filename.innerHTML=blob.files[0].name" accept="video/*" />
                </label>
                <div id="filename" style="margin-top: 10px;"></div>
            </div>
        </div>
        <div class="settings_list_row">
            <div class="settings_label">{_video_link_to_yt}</div>
            <div class="settings_labeled_text">
                <input type="text" name="link" placeholder="https://www.youtube.com/watch?v=9FWSRQEqhKE" />
            </div>
        </div>
        <div class="settings_save_footer">
            <input type="hidden" name="hash" value="{$csrfToken}" />
            <input type="submit" class="button" name="submit" value="{_upload_button}" />
        </div>
      </div>
    </form>
</div>
{/block}
